const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        const snapshotsCollection = db.collection('daily_snapshots');
        
        // Parse query parameters
        const params = new URLSearchParams(event.queryString || '');
        const dateFrom = params.get('dateFrom');
        const dateTo = params.get('dateTo');
        const period = params.get('period') || '30d';
        const metrics = params.get('metrics'); // specific metrics to return
        const limit = parseInt(params.get('limit')) || 100;

        let query = {};
        let endDate = new Date();
        let startDate;

        // Build date query
        if (dateFrom && dateTo) {
            query.date = {
                $gte: dateFrom,
                $lte: dateTo
            };
        } else {
            // Use period
            startDate = getPeriodStartDate(endDate, period);
            query.date = {
                $gte: startDate.toISOString().split('T')[0],
                $lte: endDate.toISOString().split('T')[0]
            };
        }

        // Fetch snapshots
        const snapshots = await snapshotsCollection
            .find(query)
            .sort({ date: -1 })
            .limit(limit)
            .toArray();

        // Process data based on request type
        let result;
        if (metrics) {
            result = extractSpecificMetrics(snapshots, metrics.split(','));
        } else {
            result = {
                snapshots: snapshots,
                summary: generateSummary(snapshots),
                trends: generateTrends(snapshots),
                insights: generateHistoricalInsights(snapshots)
            };
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result)
        };

    } catch (error) {
        console.error('Error fetching daily snapshots:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to fetch daily snapshots',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

function getPeriodStartDate(endDate, period) {
    const start = new Date(endDate);
    switch (period) {
        case '7d':
            start.setDate(start.getDate() - 7);
            break;
        case '30d':
            start.setDate(start.getDate() - 30);
            break;
        case '90d':
            start.setDate(start.getDate() - 90);
            break;
        case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
        default:
            start.setDate(start.getDate() - 30);
    }
    return start;
}

function extractSpecificMetrics(snapshots, metricTypes) {
    const result = {};
    
    metricTypes.forEach(metricType => {
        switch (metricType.trim()) {
            case 'tasks':
                result.tasks = snapshots.map(s => ({
                    date: s.date,
                    total: s.tasks?.total || 0,
                    completed: s.tasks?.completed || 0,
                    pending: s.tasks?.pending || 0,
                    completionRate: s.metrics?.task?.completionRate || 0
                }));
                break;
                
            case 'equipment':
                result.equipment = snapshots.map(s => ({
                    date: s.date,
                    total: s.equipment?.total || 0,
                    byStatus: s.equipment?.byStatus || {},
                    maintenanceNeeded: s.metrics?.equipment?.maintenanceNeeded || 0
                }));
                break;
                
            case 'productivity':
                result.productivity = snapshots.map(s => ({
                    date: s.date,
                    completionRate: s.metrics?.productivity?.completionRate || 0,
                    efficiency: s.metrics?.productivity?.efficiency || 0,
                    qualityScore: s.metrics?.productivity?.qualityScore || 0
                }));
                break;
                
            case 'upgrades':
                result.upgrades = snapshots.map(s => ({
                    date: s.date,
                    total: s.upgrades?.total || 0,
                    pending: s.upgrades?.pending || 0,
                    approved: s.upgrades?.approved || 0
                }));
                break;
        }
    });
    
    return result;
}

function generateSummary(snapshots) {
    if (snapshots.length === 0) {
        return {
            totalDays: 0,
            averageTasksPerDay: 0,
            averageCompletionRate: 0,
            totalTasksCompleted: 0,
            totalEquipmentManaged: 0
        };
    }

    const latest = snapshots[0]; // Most recent snapshot
    const oldest = snapshots[snapshots.length - 1];
    
    // Calculate averages
    const totalTasks = snapshots.reduce((sum, s) => sum + (s.tasks?.completed || 0), 0);
    const avgTasksPerDay = totalTasks / snapshots.length;
    
    const totalCompletionRate = snapshots.reduce((sum, s) => sum + (s.metrics?.task?.completionRate || 0), 0);
    const avgCompletionRate = totalCompletionRate / snapshots.length;
    
    // Calculate trends
    const taskTrend = calculateTrend(snapshots, 'tasks.completed');
    const completionTrend = calculateTrend(snapshots, 'metrics.task.completionRate');
    
    return {
        totalDays: snapshots.length,
        dateRange: {
            from: oldest.date,
            to: latest.date
        },
        averageTasksPerDay: Math.round(avgTasksPerDay * 10) / 10,
        averageCompletionRate: Math.round(avgCompletionRate * 10) / 10,
        totalTasksCompleted: totalTasks,
        totalEquipmentManaged: latest.equipment?.total || 0,
        trends: {
            tasks: taskTrend,
            completion: completionTrend
        }
    };
}

function generateTrends(snapshots) {
    if (snapshots.length < 2) return {};
    
    // Reverse to get chronological order
    const chronological = [...snapshots].reverse();
    
    return {
        taskCompletion: {
            labels: chronological.map(s => s.date),
            data: chronological.map(s => s.tasks?.completed || 0),
            trend: calculateTrend(chronological, 'tasks.completed')
        },
        completionRate: {
            labels: chronological.map(s => s.date),
            data: chronological.map(s => s.metrics?.task?.completionRate || 0),
            trend: calculateTrend(chronological, 'metrics.task.completionRate')
        },
        productivity: {
            labels: chronological.map(s => s.date),
            data: chronological.map(s => s.metrics?.productivity?.efficiency || 0),
            trend: calculateTrend(chronological, 'metrics.productivity.efficiency')
        },
        equipmentStatus: {
            labels: chronological.map(s => s.date),
            active: chronological.map(s => s.metrics?.equipment?.activeEquipment || 0),
            maintenance: chronological.map(s => s.metrics?.equipment?.maintenanceNeeded || 0)
        }
    };
}

function calculateTrend(snapshots, path) {
    if (snapshots.length < 2) return { direction: 'stable', change: 0 };
    
    const values = snapshots.map(s => getNestedValue(s, path) || 0);
    const first = values[0];
    const last = values[values.length - 1];
    
    if (first === 0) return { direction: 'stable', change: 0 };
    
    const change = ((last - first) / first) * 100;
    
    return {
        direction: change > 5 ? 'up' : change < -5 ? 'down' : 'stable',
        change: Math.round(change * 10) / 10,
        absolute: last - first
    };
}

function getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current?.[key], obj);
}

function generateHistoricalInsights(snapshots) {
    if (snapshots.length === 0) return [];
    
    const insights = [];
    const latest = snapshots[0];
    
    // Analyze task completion trends
    const taskTrend = calculateTrend(snapshots, 'tasks.completed');
    if (taskTrend.direction === 'up' && taskTrend.change > 10) {
        insights.push({
            type: 'positive',
            category: 'productivity',
            title: 'Increasing Task Completion',
            message: `Task completion has increased by ${taskTrend.change.toFixed(1)}% over the selected period`,
            recommendation: 'Continue current productivity practices'
        });
    } else if (taskTrend.direction === 'down' && taskTrend.change < -10) {
        insights.push({
            type: 'warning',
            category: 'productivity',
            title: 'Declining Task Completion',
            message: `Task completion has decreased by ${Math.abs(taskTrend.change).toFixed(1)}% over the selected period`,
            recommendation: 'Review workload and identify bottlenecks'
        });
    }
    
    // Analyze completion rate consistency
    const completionRates = snapshots.map(s => s.metrics?.task?.completionRate || 0);
    const avgCompletionRate = completionRates.reduce((a, b) => a + b, 0) / completionRates.length;
    const variance = calculateVariance(completionRates);
    
    if (variance < 100) { // Low variance indicates consistency
        insights.push({
            type: 'positive',
            category: 'consistency',
            title: 'Consistent Performance',
            message: `Completion rate has been consistent around ${avgCompletionRate.toFixed(1)}%`,
            recommendation: 'Maintain current workflow processes'
        });
    } else {
        insights.push({
            type: 'info',
            category: 'consistency',
            title: 'Variable Performance',
            message: 'Task completion rates show significant variation',
            recommendation: 'Identify factors causing performance fluctuations'
        });
    }
    
    // Equipment maintenance insights
    const maintenanceNeeded = latest.metrics?.equipment?.maintenanceNeeded || 0;
    if (maintenanceNeeded > 0) {
        insights.push({
            type: 'warning',
            category: 'equipment',
            title: 'Equipment Maintenance Required',
            message: `${maintenanceNeeded} equipment items currently need maintenance`,
            recommendation: 'Schedule maintenance to prevent operational disruptions'
        });
    }
    
    // Weekly patterns
    const weeklyPattern = analyzeWeeklyPatterns(snapshots);
    if (weeklyPattern.bestDay) {
        insights.push({
            type: 'info',
            category: 'patterns',
            title: 'Weekly Performance Pattern',
            message: `${weeklyPattern.bestDay} shows highest productivity`,
            recommendation: 'Consider scheduling important tasks on high-productivity days'
        });
    }
    
    return insights;
}

function calculateVariance(values) {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
    return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
}

function analyzeWeeklyPatterns(snapshots) {
    const dayTotals = {
        'Monday': { total: 0, count: 0 },
        'Tuesday': { total: 0, count: 0 },
        'Wednesday': { total: 0, count: 0 },
        'Thursday': { total: 0, count: 0 },
        'Friday': { total: 0, count: 0 },
        'Saturday': { total: 0, count: 0 },
        'Sunday': { total: 0, count: 0 }
    };
    
    snapshots.forEach(snapshot => {
        const date = new Date(snapshot.date);
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' });
        const completionRate = snapshot.metrics?.task?.completionRate || 0;
        
        if (dayTotals[dayName]) {
            dayTotals[dayName].total += completionRate;
            dayTotals[dayName].count += 1;
        }
    });
    
    // Calculate averages and find best day
    let bestDay = null;
    let bestAverage = 0;
    
    Object.keys(dayTotals).forEach(day => {
        if (dayTotals[day].count > 0) {
            const average = dayTotals[day].total / dayTotals[day].count;
            if (average > bestAverage) {
                bestAverage = average;
                bestDay = day;
            }
        }
    });
    
    return {
        bestDay,
        bestAverage: Math.round(bestAverage * 10) / 10,
        dayAverages: Object.keys(dayTotals).reduce((acc, day) => {
            acc[day] = dayTotals[day].count > 0 
                ? Math.round((dayTotals[day].total / dayTotals[day].count) * 10) / 10 
                : 0;
            return acc;
        }, {})
    };
}
