const { connectToDatabase } = require('./utils/mongodb');

exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: ''
    };
  }

  try {
    const { db } = await connectToDatabase();
    
    const pcs = await db.collection('pcs').find({}).toArray();
    
    // Convert MongoDB _id to id
    const formattedPcs = pcs.map(pc => ({
      id: pc._id.toString(),
      pcName: pc.pcName,
      processor: pc.processor,
      ram: pc.ram,
      storage: pc.storage,
      gpu: pc.gpu,
      motherboard: pc.motherboard,
      powerSupply: pc.powerSupply,
      notes: pc.notes,
      category: pc.category,
      createdAt: pc.createdAt
    }));

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: JSON.stringify(formattedPcs)
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: JSON.stringify({ error: error.message })
    };
  }
};
