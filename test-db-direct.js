// Load environment variables from .env file
require('dotenv').config();

// Direct test of the database function
const { handler } = require('./netlify/functions/test-db');

async function testDatabase() {
    console.log('Testing database function directly...\n');
    
    // Mock event and context objects
    const mockEvent = {
        httpMethod: 'GET',
        headers: {},
        body: null
    };
    
    const mockContext = {};
    
    try {
        const result = await handler(mockEvent, mockContext);
        
        console.log('Status Code:', result.statusCode);
        console.log('Headers:', JSON.stringify(result.headers, null, 2));
        console.log('Response Body:');
        
        if (result.body) {
            try {
                const parsedBody = JSON.parse(result.body);
                console.log(JSON.stringify(parsedBody, null, 2));
            } catch (e) {
                console.log(result.body);
            }
        }
        
    } catch (error) {
        console.error('Error testing database function:');
        console.error(error.message);
        console.error('\nFull error:', error);
    }
}

// Check environment variables
console.log('Environment Check:');
console.log('MONGODB_URI exists:', !!process.env.MONGODB_URI);
console.log('MONGODB_URI length:', process.env.MONGODB_URI ? process.env.MONGODB_URI.length : 0);
console.log('NODE_ENV:', process.env.NODE_ENV || 'not set');
console.log('');

testDatabase();
