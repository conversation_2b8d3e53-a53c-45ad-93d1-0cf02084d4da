<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test - Daily Work Management System</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            min-height: 100vh;
            padding: var(--space-4);
        }
        
        .test-container {
            background: white;
            padding: var(--space-8);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-result {
            margin-top: var(--space-4);
            padding: var(--space-4);
            border-radius: var(--radius-md);
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success {
            background-color: var(--success-50);
            border: 1px solid var(--success-200);
            color: var(--success-800);
        }
        
        .error {
            background-color: var(--error-50);
            border: 1px solid var(--error-200);
            color: var(--error-800);
        }
        
        .loading {
            background-color: var(--warning-50);
            border: 1px solid var(--warning-200);
            color: var(--warning-800);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-3xl font-bold text-secondary-900 mb-6">
            <i class="fas fa-database mr-3"></i>
            Database Connection Test
        </h1>
        
        <div class="mb-6">
            <p class="text-secondary-700 mb-4">
                This page will test your MongoDB database connection and display the results.
            </p>
            
            <button id="testBtn" class="btn btn-primary">
                <i class="fas fa-play mr-2"></i>
                Test Database Connection
            </button>
            
            <button id="refreshBtn" class="btn btn-secondary ml-3" style="display: none;">
                <i class="fas fa-refresh mr-2"></i>
                Test Again
            </button>
        </div>
        
        <div id="testResult" class="test-result" style="display: none;"></div>
        
        <div class="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 class="font-semibold text-secondary-900 mb-2">What this test checks:</h3>
            <ul class="list-disc list-inside text-secondary-700 space-y-1">
                <li>MongoDB connection string environment variable</li>
                <li>Database connectivity</li>
                <li>Available collections in your database</li>
                <li>Basic database operations</li>
            </ul>
        </div>
        
        <div class="mt-4 text-center">
            <a href="index.html" class="btn btn-outline">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <script>
        const testBtn = document.getElementById('testBtn');
        const refreshBtn = document.getElementById('refreshBtn');
        const testResult = document.getElementById('testResult');

        async function testDatabase() {
            testBtn.disabled = true;
            testBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
            
            testResult.style.display = 'block';
            testResult.className = 'test-result loading';
            testResult.textContent = 'Testing database connection...\nPlease wait...';

            try {
                const response = await fetch('/.netlify/functions/test-db', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    testResult.className = 'test-result success';
                    testResult.textContent = `✅ Database Connection Successful!

Connection Details:
- Environment variable exists: ${data.envVarExists ? 'Yes' : 'No'}
- Database: pc-inventory
- Status: Connected successfully

Collections found (${data.collections.length}):
${data.collections.map(name => `- ${name}`).join('\n')}

Message: ${data.message}`;
                } else {
                    testResult.className = 'test-result error';
                    testResult.textContent = `❌ Database Connection Failed

Error Details:
${data.error || 'Unknown error occurred'}

Environment Check:
- MONGODB_URI exists: ${data.envVarExists ? 'Yes' : 'No'}
- URI length: ${data.mongoUriLength || 0} characters

Troubleshooting:
1. Check if MONGODB_URI environment variable is set in Netlify
2. Verify the MongoDB connection string is correct
3. Ensure your MongoDB cluster allows connections from Netlify
4. Check MongoDB cluster status`;
                }
            } catch (error) {
                testResult.className = 'test-result error';
                testResult.textContent = `❌ Network Error

Failed to connect to the test endpoint.

Error: ${error.message}

Possible causes:
1. Netlify functions not deployed
2. Network connectivity issues
3. Function endpoint not accessible
4. CORS configuration issues

If testing locally, make sure you're running:
netlify dev`;
            }

            testBtn.disabled = false;
            testBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Test Database Connection';
            refreshBtn.style.display = 'inline-block';
        }

        testBtn.addEventListener('click', testDatabase);
        refreshBtn.addEventListener('click', testDatabase);

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testDatabase, 500);
        });
    </script>
</body>
</html>
