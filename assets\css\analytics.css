/* Analytics Dashboard Enhancements */

/* Advanced Chart Styling */
.chart-card {
    position: relative;
    overflow: hidden;
}

.chart-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    z-index: 1;
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    margin-top: var(--space-4);
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-sm);
}

/* Enhanced Metric Cards */
.metric-card {
    background: linear-gradient(135deg, white 0%, var(--secondary-50) 100%);
    position: relative;
    overflow: hidden;
}

.metric-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
    border-radius: 50%;
}

.metric-trend {
    display: flex;
    align-items: center;
    gap: var(--space-1);
    margin-top: var(--space-2);
}

.trend-arrow {
    font-size: var(--font-size-sm);
}

.trend-up {
    color: var(--success-600);
}

.trend-down {
    color: var(--error-600);
}

.trend-neutral {
    color: var(--secondary-500);
}

/* Data Table Styling */
.analytics-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--space-4);
}

.analytics-table th,
.analytics-table td {
    padding: var(--space-3) var(--space-4);
    text-align: left;
    border-bottom: 1px solid var(--secondary-200);
}

.analytics-table th {
    background: var(--secondary-50);
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-700);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.analytics-table tbody tr:hover {
    background: var(--secondary-50);
}

.analytics-table .number {
    font-weight: var(--font-weight-semibold);
    color: var(--secondary-900);
}

.analytics-table .positive {
    color: var(--success-600);
}

.analytics-table .negative {
    color: var(--error-600);
}

/* Progress Bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--secondary-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin: var(--space-2) 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

.progress-fill.success {
    background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.progress-fill.error {
    background: linear-gradient(90deg, var(--error-500), var(--error-600));
}

/* KPI Cards */
.kpi-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    margin: var(--space-6) 0;
}

.kpi-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    border: 1px solid var(--secondary-200);
    text-align: center;
    transition: all var(--transition-normal);
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.kpi-value {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-600);
    margin-bottom: var(--space-1);
}

.kpi-label {
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Insight Cards */
.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin: var(--space-8) 0;
}

.insight-card {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    border: 1px solid var(--secondary-200);
    box-shadow: var(--shadow-sm);
}

.insight-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
}

.insight-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: white;
    background: var(--primary-600);
}

.insight-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--secondary-900);
    margin: 0;
}

.insight-content {
    color: var(--secondary-700);
    line-height: var(--line-height-relaxed);
}

.insight-recommendations {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 1px solid var(--secondary-200);
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: var(--space-2);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--secondary-600);
}

.recommendation-item i {
    color: var(--primary-600);
    margin-top: 2px;
}

/* Filter Controls */
.filter-bar {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    border: 1px solid var(--secondary-200);
    box-shadow: var(--shadow-sm);
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-4);
    align-items: end;
}

.filter-actions {
    display: flex;
    gap: var(--space-2);
    justify-content: flex-end;
}

/* Export Controls */
.export-section {
    background: var(--secondary-50);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-top: var(--space-6);
}

.export-options {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.export-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    background: white;
    border: 1px solid var(--secondary-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.export-btn:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    color: var(--primary-700);
}

/* Real-time Indicators */
.live-indicator {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-size: var(--font-size-sm);
    color: var(--success-600);
    margin-left: var(--space-2);
}

.live-dot {
    width: 8px;
    height: 8px;
    background: var(--success-600);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Comparison Charts */
.comparison-section {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin: var(--space-6) 0;
    border: 1px solid var(--secondary-200);
}

.comparison-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-6);
}

.comparison-controls {
    display: flex;
    gap: var(--space-2);
}

.period-selector {
    display: flex;
    background: var(--secondary-100);
    border-radius: var(--radius-md);
    padding: var(--space-1);
}

.period-option {
    padding: var(--space-2) var(--space-3);
    border: none;
    background: transparent;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.period-option.active {
    background: white;
    box-shadow: var(--shadow-sm);
    color: var(--primary-600);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .kpi-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-3);
    }
    
    .insights-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .filter-row {
        grid-template-columns: 1fr;
    }
    
    .filter-actions {
        justify-content: stretch;
    }
    
    .filter-actions .btn {
        flex: 1;
    }
    
    .export-options {
        justify-content: center;
    }
    
    .comparison-header {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }
    
    .period-selector {
        justify-content: center;
    }
}

/* Print Styles for Reports */
@media print {
    .chart-card,
    .metric-card,
    .insight-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .refresh-button,
    .filter-bar,
    .export-section {
        display: none;
    }
    
    .dashboard-container {
        max-width: none;
        padding: 0;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
}
