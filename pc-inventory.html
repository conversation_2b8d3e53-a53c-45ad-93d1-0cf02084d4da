<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PC Inventory & Upgrade Management - Work Management System</title>

    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- SQL.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sql.js/1.8.0/sql-wasm.js"></script>

    <style>
        /* Screen styles */
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 10px;
        }

        .date {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 18px;
        }

        .tabs {
            display: flex;
            margin-bottom: 30px;
            border-bottom: 2px solid #ddd;
        }

        .tab {
            padding: 15px 30px;
            background: #f8f9fa;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
        }

        .tab.active {
            background: #007bff;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .add-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }

        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            flex: 1;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }

        textarea {
            resize: vertical;
            min-height: 60px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
            width: 100%;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .item-list {
            margin-bottom: 30px;
        }

        .item {
            padding: 20px;
            border-bottom: 1px solid #eee;
            font-size: 14px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .item-name {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }

        .item-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 10px;
        }

        .item-details div {
            color: #666;
        }

        .item-details strong {
            color: #333;
        }

        .item-specs {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            color: #666;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }

        .upgrade-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 5px;
        }

        .controls {
            text-align: center;
            margin: 30px 0;
        }

        .print-btn {
            background: #28a745;
            color: white;
        }

        .clear-btn {
            background: #6c757d;
            color: white;
        }

        .signature-section {
            margin-top: 40px;
            padding: 20px 0;
            border-top: 2px solid #ddd;
        }

        .signature-line {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-top: 30px;
        }

        .signature-box {
            border-bottom: 2px solid #333;
            width: 200px;
            height: 30px;
        }

        .date-box {
            border-bottom: 2px solid #333;
            width: 120px;
            height: 30px;
        }

        .date-label {
            margin-left: 30px;
        }

        .upgrade-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        /* Filter styles */
        .filter-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        .filter-btn.active {
            background: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3);
        }

        .filter-controls {
            border: 1px solid #dee2e6;
        }

        /* Dashboard styles */
        .dashboard-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: 1fr;
        }

        .stats-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 2.5em;
            margin-right: 15px;
            opacity: 0.9;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            margin-top: 5px;
        }

        .dashboard-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .dashboard-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
        }

        .category-item {
            margin-bottom: 15px;
        }

        .category-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .category-name {
            font-weight: 500;
            min-width: 80px;
        }

        .category-progress {
            display: flex;
            align-items: center;
            flex: 1;
            margin-left: 15px;
        }

        .progress-bar {
            flex: 1;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin-right: 10px;
        }

        .progress-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .admin-color { background: #007bff; }
        .graphic-color { background: #28a745; }
        .emp-color { background: #ffc107; }

        .category-count {
            font-weight: bold;
            min-width: 30px;
            text-align: center;
        }

        .activity-feed {
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            font-size: 1.5em;
            margin-right: 15px;
            opacity: 0.7;
        }

        .activity-content {
            flex: 1;
        }

        .activity-text {
            font-weight: 500;
            margin-bottom: 2px;
        }

        .activity-time {
            font-size: 0.8em;
            color: #666;
        }

        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }

        .action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #495057;
            font-size: 14px;
        }

        .action-btn:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .action-icon {
            margin-right: 8px;
            font-size: 1.2em;
        }

        /* Equipment request styles */
        .priority-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            color: white;
        }

        .priority-low {
            background-color: #28a745;
        }

        .priority-medium {
            background-color: #ffc107;
            color: #212529;
        }

        .priority-high {
            background-color: #fd7e14;
        }

        .priority-critical {
            background-color: #dc3545;
        }

        .equipment-status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-left: 10px;
        }

        .status-pending {
            background-color: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }

        .status-approved {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-ordered {
            background-color: #cff4fc;
            color: #055160;
        }

        .status-delivered {
            background-color: #d1e7dd;
            color: #0f5132;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #842029;
        }

        @media (max-width: 768px) {
            .stats-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .quick-actions {
                grid-template-columns: 1fr;
            }
        }

        /* Print styles */
        @media print {
            @page {
                margin: 0.5in;
                size: auto;
            }
            
            body {
                background: white;
                margin: 0;
                padding: 0;
            }

            .container {
                box-shadow: none;
                padding: 20px;
            }

            .add-section, .controls, .delete-btn, .upgrade-btn, .tabs, .btn, #editModal, #selectedPCSpecs, .filter-controls, #dashboard {
                display: none !important;
            }

            .item {
                border-bottom: 1px solid #ccc;
                page-break-inside: avoid;
            }

            .signature-section {
                page-break-inside: avoid;
                margin-top: 40px;
                border-top: 2px solid #333;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PC Inventory & Upgrade Management</h1>
        <div class="date" id="currentDate"></div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('dashboard')">Dashboard</button>
            <button class="tab" onclick="showTab('inventory')">PC Inventory</button>
            <button class="tab" onclick="showTab('upgrades')">Upgrade Requests</button>
            <button class="tab" onclick="showTab('equipment')">Equipment Requests</button>
        </div>

        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="dashboard-grid">
                <!-- Summary Cards -->
                <div class="stats-row">
                    <div class="stat-card">
                        <div class="stat-icon">💻</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalPCs">0</div>
                            <div class="stat-label">Total PCs</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-content">
                            <div class="stat-number" id="pendingUpgrades">0</div>
                            <div class="stat-label">Pending Upgrades</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">🛒</div>
                        <div class="stat-content">
                            <div class="stat-number" id="pendingEquipment">0</div>
                            <div class="stat-label">Equipment Requests</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">✅</div>
                        <div class="stat-content">
                            <div class="stat-number" id="completedTodos">0</div>
                            <div class="stat-label">Completed Tasks</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">📋</div>
                        <div class="stat-content">
                            <div class="stat-number" id="totalTodos">0</div>
                            <div class="stat-label">Total Tasks</div>
                        </div>
                    </div>
                </div>

                <!-- Category Breakdown -->
                <div class="dashboard-section">
                    <h3>PC Categories</h3>
                    <div class="category-stats" id="categoryStats">
                        <div class="category-item">
                            <div class="category-bar">
                                <div class="category-name">Admin</div>
                                <div class="category-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill admin-color" id="adminProgress"></div>
                                    </div>
                                    <span class="category-count" id="adminCount">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-bar">
                                <div class="category-name">Graphic</div>
                                <div class="category-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill graphic-color" id="graphicProgress"></div>
                                    </div>
                                    <span class="category-count" id="graphicCount">0</span>
                                </div>
                            </div>
                        </div>

                        <div class="category-item">
                            <div class="category-bar">
                                <div class="category-name">EMP</div>
                                <div class="category-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill emp-color" id="empProgress"></div>
                                    </div>
                                    <span class="category-count" id="empCount">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section">
                    <h3>Recent Activity</h3>
                    <div class="activity-feed" id="activityFeed">
                        <div class="activity-item">
                            <div class="activity-icon">📊</div>
                            <div class="activity-content">
                                <div class="activity-text">Dashboard loaded successfully</div>
                                <div class="activity-time">Just now</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="dashboard-section">
                    <h3>Quick Actions</h3>
                    <div class="quick-actions">
                        <button class="action-btn" onclick="showTab('inventory')">
                            <span class="action-icon">➕</span>
                            Add New PC
                        </button>
                        <button class="action-btn" onclick="showTab('upgrades')">
                            <span class="action-icon">🔧</span>
                            Request Upgrade
                        </button>
                        <button class="action-btn" onclick="window.open('todo-list.html', '_blank')">
                            <span class="action-icon">📝</span>
                            Manage Tasks
                        </button>
                        <button class="action-btn" onclick="refreshDashboard()">
                            <span class="action-icon">🔄</span>
                            Refresh Data
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Equipment Request Tab -->
        <div id="equipment" class="tab-content">
            <div class="add-section">
                <h2>Request New Equipment</h2>
                <div class="form-row">
                    <div class="form-group">
                        <label for="equipmentType">Equipment Type:</label>
                        <select id="equipmentType">
                            <option value="">Select type...</option>
                            <option value="Monitor">Monitor</option>
                            <option value="Keyboard">Keyboard</option>
                            <option value="Mouse">Mouse</option>
                            <option value="Headset">Headset</option>
                            <option value="Printer">Printer</option>
                            <option value="Scanner">Scanner</option>
                            <option value="Docking Station">Docking Station</option>
                            <option value="External Drive">External Drive</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="equipmentName">Equipment Name/Model:</label>
                        <input type="text" id="equipmentName" placeholder="e.g., Dell P2419H, Logitech MX Keys...">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="equipmentQuantity">Quantity:</label>
                        <input type="number" id="equipmentQuantity" min="1" value="1">
                    </div>
                    <div class="form-group">
                        <label for="equipmentPriority">Priority:</label>
                        <select id="equipmentPriority">
                            <option value="Low">Low</option>
                            <option value="Medium" selected>Medium</option>
                            <option value="High">High</option>
                            <option value="Critical">Critical</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="equipmentForUser">For User:</label>
                        <input type="text" id="equipmentForUser" placeholder="User name or department">
                    </div>
                </div>

                <div class="form-group">
                    <label for="equipmentReason">Reason for Request:</label>
                    <textarea id="equipmentReason" placeholder="Why is this equipment needed? How will it improve productivity or solve a problem?"></textarea>
                </div>

                <div class="form-group">
                    <label for="equipmentSpecs">Specifications/Requirements:</label>
                    <textarea id="equipmentSpecs" placeholder="Any specific requirements or specifications needed?"></textarea>
                </div>

                <button class="btn btn-primary" id="addEquipmentBtn">Submit Request</button>
            </div>

            <div class="controls">
                <button class="btn print-btn" onclick="window.print()">Print Requests</button>
                <button class="btn clear-btn" onclick="clearAllEquipment()">Clear All Requests</button>
            </div>

            <div class="item-list" id="equipmentList"></div>
        </div>

        <!-- PC Inventory Tab -->
        <div id="inventory" class="tab-content active">
            <div class="add-section">
                <h3>Add New PC</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="pcName">PC Name/ID:</label>
                        <input type="text" id="pcName" placeholder="e.g., DESK-001, John's PC..." maxlength="100">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="processor">Processor:</label>
                        <input type="text" id="processor" placeholder="e.g., Intel i7-12700K, AMD Ryzen 5..." maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="ram">RAM:</label>
                        <input type="text" id="ram" placeholder="e.g., 16GB DDR4, 32GB DDR5..." maxlength="50">
                    </div>
                    <div class="form-group">
                        <label for="storage">Storage:</label>
                        <input type="text" id="storage" placeholder="e.g., 512GB SSD, 1TB HDD..." maxlength="100">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="gpu">GPU:</label>
                        <input type="text" id="gpu" placeholder="e.g., RTX 4070, GTX 1660..." maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="motherboard">Motherboard:</label>
                        <input type="text" id="motherboard" placeholder="e.g., ASUS B550, MSI Z690..." maxlength="100">
                    </div>
                    <div class="form-group">
                        <label for="powerSupply">Power Supply:</label>
                        <input type="text" id="powerSupply" placeholder="e.g., 650W 80+ Gold..." maxlength="100">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="category">PC Category:</label>
                        <select id="category">
                            <option value="">Select category...</option>
                            <option value="Admin">Admin</option>
                            <option value="Graphic">Graphic</option>
                            <option value="EMP">EMP (Employee)</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="notes">Additional Notes:</label>
                    <textarea id="notes" placeholder="Any additional information, issues, or specifications..."></textarea>
                </div>

                <button class="btn btn-primary" id="addPcBtn">Add PC</button>
            </div>

            <div class="controls">
                <button class="btn print-btn" onclick="window.print()">Print Inventory</button>
                <button class="btn clear-btn" onclick="clearAllPCs()">Clear All PCs</button>
            </div>

            <!-- Filter Controls -->
            <div class="filter-controls" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px;">
                <h4 style="margin: 0 0 10px 0; color: #495057;">Filter by Category:</h4>
                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button class="btn filter-btn active" onclick="filterPCs('all')" id="filter-all">All PCs</button>
                    <button class="btn filter-btn" onclick="filterPCs('Admin')" id="filter-admin">Admin</button>
                    <button class="btn filter-btn" onclick="filterPCs('Graphic')" id="filter-graphic">Graphic</button>
                    <button class="btn filter-btn" onclick="filterPCs('EMP')" id="filter-emp">EMP</button>
                </div>
            </div>

            <div class="item-list" id="pcList"></div>
        </div>

        <!-- Upgrade Requests Tab -->
        <div id="upgrades" class="tab-content">
            <div class="add-section">
                <h3>Request PC Upgrade</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="selectPC">Select PC:</label>
                        <select id="selectPC">
                            <option value="">Choose a PC...</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="upgradePart">Part to Upgrade:</label>
                        <select id="upgradePart">
                            <option value="">Select part...</option>
                            <option value="processor">Processor</option>
                            <option value="ram">RAM</option>
                            <option value="storage">Storage</option>
                            <option value="gpu">GPU</option>
                            <option value="motherboard">Motherboard</option>
                            <option value="powerSupply">Power Supply</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                </div>

                <!-- PC Specifications Display -->
                <div id="selectedPCSpecs" style="display: none; margin: 20px 0; padding: 15px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px;">
                    <h4 style="margin-top: 0; color: #495057;">Current PC Specifications:</h4>
                    <div id="pcSpecsContent"></div>
                </div>

                <div class="form-group">
                    <label for="upgradeReason">Reason for Upgrade:</label>
                    <textarea id="upgradeReason" placeholder="Why is this upgrade needed? Performance issues, compatibility, etc..."></textarea>
                </div>

                <div class="form-group">
                    <label for="futureSpec">Future Specification:</label>
                    <input type="text" id="futureSpec" placeholder="What specific component/spec do you want to upgrade to?">
                </div>

                <button class="btn btn-primary" id="addUpgradeBtn">Request Upgrade</button>
            </div>

            <div class="controls">
                <button class="btn print-btn" onclick="window.print()">Print Requests</button>
                <button class="btn clear-btn" onclick="clearAllUpgrades()">Clear All Requests</button>
            </div>

            <div class="item-list" id="upgradeList"></div>
        </div>

        <!-- Edit PC Modal -->
        <div id="editModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 600px; max-height: 90%; overflow-y: auto;">
                <h3>Edit PC</h3>
                <div class="form-group">
                    <label for="editPcName">PC Name:</label>
                    <input type="text" id="editPcName">
                </div>
                <div class="form-group">
                    <label for="editProcessor">Processor:</label>
                    <input type="text" id="editProcessor">
                </div>
                <div class="form-group">
                    <label for="editRam">RAM:</label>
                    <input type="text" id="editRam">
                </div>
                <div class="form-group">
                    <label for="editStorage">Storage:</label>
                    <input type="text" id="editStorage">
                </div>
                <div class="form-group">
                    <label for="editGpu">GPU:</label>
                    <input type="text" id="editGpu">
                </div>
                <div class="form-group">
                    <label for="editMotherboard">Motherboard:</label>
                    <input type="text" id="editMotherboard">
                </div>
                <div class="form-group">
                    <label for="editPowerSupply">Power Supply:</label>
                    <input type="text" id="editPowerSupply">
                </div>
                <div class="form-group">
                    <label for="editCategory">PC Category:</label>
                    <select id="editCategory">
                        <option value="">Select category...</option>
                        <option value="Admin">Admin</option>
                        <option value="Graphic">Graphic</option>
                        <option value="EMP">EMP (Employee)</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editNotes">Notes:</label>
                    <textarea id="editNotes"></textarea>
                </div>
                <div style="margin-top: 20px;">
                    <button class="btn btn-primary" onclick="saveEditedPC()">Save Changes</button>
                    <button class="btn btn-secondary" onclick="closeEditModal()" style="margin-left: 10px;">Cancel</button>
                </div>
            </div>
        </div>

        <div class="signature-section">
            <div class="signature-line">
                <span>IT Administrator Signature: </span>
                <div class="signature-box"></div>
                <span class="date-label">Date: </span>
                <div class="date-box"></div>
            </div>
            <div class="signature-line">
                <span>Supervisor Signature: </span>
                <div class="signature-box"></div>
                <span class="date-label">Date: </span>
                <div class="date-box"></div>
            </div>
        </div>
    </div>

    <script>
        // API base URL - will be your Netlify site URL
        const API_BASE = '/.netlify/functions';
        
        let pcs = [];
        let upgrades = [];
        let equipment = [];
        
        // Load PCs from API
        async function loadPCs() {
            try {
                const response = await fetch(`${API_BASE}/get-pcs`);
                if (response.ok) {
                    pcs = await response.json();
                    renderPCs();
                    updatePCDropdown();
                }
            } catch (error) {
                console.error('Error loading PCs:', error);
            }
        }
        
        // Load upgrades from API
        async function loadUpgrades() {
            try {
                const response = await fetch(`${API_BASE}/get-upgrades`);
                if (response.ok) {
                    upgrades = await response.json();
                    renderUpgrades();
                }
            } catch (error) {
                console.error('Error loading upgrades:', error);
            }
        }
        
        // Save PC to API
        async function savePC(pc) {
            try {
                const response = await fetch(`${API_BASE}/add-pc`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(pc)
                });
                
                if (response.ok) {
                    const newPC = await response.json();
                    pcs.push(newPC);
                    renderPCs();
                    updatePCDropdown();
                    return newPC;
                }
            } catch (error) {
                console.error('Error saving PC:', error);
                alert('Error saving PC. Please try again.');
            }
        }
        
        // Save upgrade to API
        async function saveUpgrade(upgrade) {
            try {
                const response = await fetch(`${API_BASE}/add-upgrade`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(upgrade)
                });
                
                if (response.ok) {
                    const newUpgrade = await response.json();
                    upgrades.push(newUpgrade);
                    renderUpgrades();
                    renderPCs();
                    return newUpgrade;
                }
            } catch (error) {
                console.error('Error saving upgrade:', error);
                alert('Error saving upgrade. Please try again.');
            }
        }
        
        // Delete PC from API
        async function deletePCFromAPI(id) {
            try {
                const response = await fetch(`${API_BASE}/delete-pc`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id })
                });
                
                if (response.ok) {
                    // Remove from local arrays
                    pcs = pcs.filter(pc => pc.id !== id);
                    upgrades = upgrades.filter(upgrade => upgrade.pcId !== id);
                    renderPCs();
                    renderUpgrades();
                    updatePCDropdown();
                }
            } catch (error) {
                console.error('Error deleting PC:', error);
                alert('Error deleting PC. Please try again.');
            }
        }
        
        // Delete upgrade from API
        async function deleteUpgradeFromAPI(id) {
            try {
                const response = await fetch(`${API_BASE}/delete-upgrade`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id })
                });
                
                if (response.ok) {
                    // Remove from local array
                    upgrades = upgrades.filter(upgrade => upgrade.id !== id);
                    renderUpgrades();
                    renderPCs();
                }
            } catch (error) {
                console.error('Error deleting upgrade:', error);
                alert('Error deleting upgrade. Please try again.');
            }
        }

        // Equipment request functions
        async function loadEquipment() {
            try {
                const response = await fetch(`${API_BASE}/get-equipment`);
                if (response.ok) {
                    equipment = await response.json();
                    renderEquipment();
                }
            } catch (error) {
                console.error('Error loading equipment:', error);
            }
        }

        async function saveEquipment(equipmentData) {
            try {
                const response = await fetch(`${API_BASE}/add-equipment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(equipmentData)
                });

                if (response.ok) {
                    const newEquipment = await response.json();
                    equipment.push(newEquipment);
                    renderEquipment();
                    return newEquipment;
                }
            } catch (error) {
                console.error('Error saving equipment:', error);
                alert('Error saving equipment request. Please try again.');
            }
        }

        async function deleteEquipmentFromAPI(id) {
            try {
                const response = await fetch(`${API_BASE}/delete-equipment`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id })
                });

                if (response.ok) {
                    // Remove from local array
                    equipment = equipment.filter(item => item.id !== id);
                    renderEquipment();
                }
            } catch (error) {
                console.error('Error deleting equipment:', error);
                alert('Error deleting equipment request. Please try again.');
            }
        }

        async function updateEquipmentStatus(id, status) {
            try {
                const response = await fetch(`${API_BASE}/update-equipment-status`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id, status })
                });

                if (response.ok) {
                    // Update local array
                    const equipmentIndex = equipment.findIndex(item => item.id === id);
                    if (equipmentIndex !== -1) {
                        equipment[equipmentIndex].status = status;
                        renderEquipment();
                    }
                }
            } catch (error) {
                console.error('Error updating equipment status:', error);
                alert('Error updating equipment status. Please try again.');
            }
        }

        async function addEquipment() {
            const type = document.getElementById('equipmentType').value;
            const name = document.getElementById('equipmentName').value.trim();
            const quantity = parseInt(document.getElementById('equipmentQuantity').value);
            const priority = document.getElementById('equipmentPriority').value;
            const forUser = document.getElementById('equipmentForUser').value.trim();
            const reason = document.getElementById('equipmentReason').value.trim();
            const specifications = document.getElementById('equipmentSpecs').value.trim();

            if (type || name || quantity || priority || forUser || reason || specifications) {
                const newEquipment = {
                    type,
                    name,
                    quantity,
                    priority,
                    forUser,
                    reason,
                    specifications
                };

                const savedEquipment = await saveEquipment(newEquipment);

                if (savedEquipment) {
                    // Clear inputs
                    document.getElementById('equipmentType').value = '';
                    document.getElementById('equipmentName').value = '';
                    document.getElementById('equipmentQuantity').value = '1';
                    document.getElementById('equipmentPriority').value = 'Medium';
                    document.getElementById('equipmentForUser').value = '';
                    document.getElementById('equipmentReason').value = '';
                    document.getElementById('equipmentSpecs').value = '';

                    // Update dashboard
                    updateDashboard();
                }
            } else {
                alert('Please fill in at least one field');
            }
        }

        function renderEquipment() {
            const equipmentList = document.getElementById('equipmentList');
            equipmentList.innerHTML = '';

            equipment.forEach((item, index) => {
                const equipmentDiv = document.createElement('div');
                equipmentDiv.className = 'item';
                equipmentDiv.innerHTML = `
                    <div class="item-header">
                        <div class="item-name">
                            ${item.name || 'Unnamed Equipment'}
                            ${item.priority ? `<span class="priority-badge priority-${item.priority.toLowerCase()}">${item.priority}</span>` : ''}
                            ${item.status ? `<span class="equipment-status status-${item.status.toLowerCase().replace(' ', '-')}">${item.status}</span>` : ''}
                        </div>
                        <div>
                            <select onchange="updateEquipmentStatus('${item.id}', this.value)" style="margin-right: 10px;">
                                <option value="Pending" ${item.status === 'Pending' ? 'selected' : ''}>Pending</option>
                                <option value="Approved" ${item.status === 'Approved' ? 'selected' : ''}>Approved</option>
                                <option value="Ordered" ${item.status === 'Ordered' ? 'selected' : ''}>Ordered</option>
                                <option value="Delivered" ${item.status === 'Delivered' ? 'selected' : ''}>Delivered</option>
                                <option value="Rejected" ${item.status === 'Rejected' ? 'selected' : ''}>Rejected</option>
                            </select>
                            <button class="delete-btn" onclick="deleteEquipment(${index})">Delete</button>
                        </div>
                    </div>
                    <div class="item-details">
                        ${item.type ? `<div><strong>Type:</strong> ${item.type}</div>` : ''}
                        ${item.quantity ? `<div><strong>Quantity:</strong> ${item.quantity}</div>` : ''}
                        ${item.forUser ? `<div><strong>For User:</strong> ${item.forUser}</div>` : ''}
                        ${item.reason ? `<div><strong>Reason:</strong> ${item.reason}</div>` : ''}
                        ${item.specifications ? `<div><strong>Specifications:</strong> ${item.specifications}</div>` : ''}
                        ${item.createdAt ? `<div><strong>Requested:</strong> ${new Date(item.createdAt).toLocaleDateString()}</div>` : ''}
                    </div>
                `;
                equipmentList.appendChild(equipmentDiv);
            });
        }

        function deleteEquipment(index) {
            if (confirm('Are you sure you want to delete this equipment request?')) {
                const equipmentId = equipment[index].id;
                deleteEquipmentFromAPI(equipmentId);
            }
        }

        function clearAllEquipment() {
            if (confirm('Are you sure you want to clear all equipment requests?')) {
                // Delete all equipment via API
                Promise.all(equipment.map(item => deleteEquipmentFromAPI(item.id)))
                    .then(() => {
                        equipment = [];
                        renderEquipment();
                    });
            }
        }

        // Initialize app
        async function initApp() {
            // Set current date
            document.getElementById('currentDate').textContent = 
                new Date().toLocaleDateString('en-US', { 
                    weekday: 'long', 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                });
            
            // Load data from API
            try {
                await loadPCs();
                await loadUpgrades();
                await loadEquipment();
                // Update dashboard after data is loaded
                await updateDashboard();
            } catch (error) {
                console.error('Failed to initialize app:', error);
                alert('Failed to load data. Please check your internet connection and try refreshing the page.');
            }
        }
        
        // Call initApp when page loads
        document.addEventListener('DOMContentLoaded', initApp);
        
        // Modified addPC function
        async function addPC() {
            const pcName = document.getElementById('pcName').value.trim();
            const processor = document.getElementById('processor').value.trim();
            const ram = document.getElementById('ram').value.trim();
            const storage = document.getElementById('storage').value.trim();
            const gpu = document.getElementById('gpu').value.trim();
            const motherboard = document.getElementById('motherboard').value.trim();
            const powerSupply = document.getElementById('powerSupply').value.trim();
            const notes = document.getElementById('notes').value.trim();
            const category = document.getElementById('category').value;

            if (pcName || processor || ram || storage || gpu || motherboard || powerSupply || notes || category) {
                const newPC = {
                    pcName,
                    processor,
                    ram,
                    storage,
                    gpu,
                    motherboard,
                    powerSupply,
                    notes,
                    category
                };
                
                // Save to API
                const savedPC = await savePC(newPC);
                
                if (savedPC) {
                    // Clear all inputs
                    document.getElementById('pcName').value = '';
                    document.getElementById('processor').value = '';
                    document.getElementById('ram').value = '';
                    document.getElementById('storage').value = '';
                    document.getElementById('gpu').value = '';
                    document.getElementById('motherboard').value = '';
                    document.getElementById('powerSupply').value = '';
                    document.getElementById('notes').value = '';
                    document.getElementById('category').value = '';

                    // Update dashboard
                    updateDashboard();
                }
            } else {
                alert('Please fill in at least one field');
            }
        }
        
        // Modified addUpgrade function
        async function addUpgrade() {
            const pcIndex = parseInt(document.getElementById('selectPC').value);
            const part = document.getElementById('upgradePart').value;
            const reason = document.getElementById('upgradeReason').value.trim();
            const futureSpec = document.getElementById('futureSpec').value.trim();

            if (pcIndex >= 0 && part && reason) {
                const selectedPC = pcs[pcIndex];
                const newUpgrade = {
                    pcId: selectedPC.id,
                    part,
                    reason: reason + (futureSpec ? ` | Future Spec: ${futureSpec}` : '')
                };

                // Save to API
                const savedUpgrade = await saveUpgrade(newUpgrade);

                if (savedUpgrade) {
                    // Clear inputs
                    document.getElementById('selectPC').value = '';
                    document.getElementById('upgradePart').value = '';
                    document.getElementById('upgradeReason').value = '';
                    document.getElementById('futureSpec').value = '';

                    // Update dashboard
                    updateDashboard();
                }
            } else {
                alert('Please fill in all fields');
            }
        }
        
        // Modified deletePC function
        async function deletePC(index) {
            if (confirm('Are you sure you want to delete this PC and all its upgrade requests?')) {
                const pcId = pcs[index].id;
                await deletePCFromAPI(pcId);
            }
        }

        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            if (tabName === 'upgrades') {
                updatePCDropdown();
            } else if (tabName === 'dashboard') {
                updateDashboard();
            } else if (tabName === 'equipment') {
                renderEquipment();
            }
        }

        function updatePCDropdown() {
            const selectPC = document.getElementById('selectPC');
            selectPC.innerHTML = '<option value="">Choose a PC...</option>';

            pcs.forEach((pc, index) => {
                const option = document.createElement('option');
                option.value = index;
                option.textContent = pc.pcName || 'Unnamed PC';
                selectPC.appendChild(option);
            });

            // Add event listener to show PC specs when selected
            selectPC.onchange = function() {
                showSelectedPCSpecs(this.value);
            };
        }

        function showSelectedPCSpecs(pcIndex) {
            const specsDiv = document.getElementById('selectedPCSpecs');
            const specsContent = document.getElementById('pcSpecsContent');

            if (pcIndex === '' || pcIndex < 0 || pcIndex >= pcs.length) {
                specsDiv.style.display = 'none';
                return;
            }

            const pc = pcs[pcIndex];
            specsContent.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    ${pc.pcName ? `<div><strong>PC Name:</strong> ${pc.pcName}</div>` : ''}
                    ${pc.category ? `<div><strong>Category:</strong> ${pc.category}</div>` : ''}
                    ${pc.processor ? `<div><strong>Processor:</strong> ${pc.processor}</div>` : ''}
                    ${pc.ram ? `<div><strong>RAM:</strong> ${pc.ram}</div>` : ''}
                    ${pc.storage ? `<div><strong>Storage:</strong> ${pc.storage}</div>` : ''}
                    ${pc.gpu ? `<div><strong>GPU:</strong> ${pc.gpu}</div>` : ''}
                    ${pc.motherboard ? `<div><strong>Motherboard:</strong> ${pc.motherboard}</div>` : ''}
                    ${pc.powerSupply ? `<div><strong>Power Supply:</strong> ${pc.powerSupply}</div>` : ''}
                    ${pc.notes ? `<div style="grid-column: 1 / -1;"><strong>Notes:</strong> ${pc.notes}</div>` : ''}
                </div>
            `;
            specsDiv.style.display = 'block';
        }

        // Filter functionality
        let currentFilter = 'all';

        function filterPCs(category) {
            currentFilter = category;

            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`filter-${category === 'all' ? 'all' : category.toLowerCase()}`).classList.add('active');

            renderPCs();
        }

        function renderPCs() {
            const pcList = document.getElementById('pcList');
            pcList.innerHTML = '';

            // Filter PCs based on current filter
            const filteredPCs = currentFilter === 'all' ? pcs : pcs.filter(pc => pc.category === currentFilter);

            filteredPCs.forEach((pc, index) => {
                // Find the original index in the pcs array for proper function calls
                const originalIndex = pcs.findIndex(p => p.id === pc.id);
                const pcDiv = document.createElement('div');
                pcDiv.className = 'item';
                
                // Check for pending upgrades by PC ID
                const pendingUpgrades = upgrades.filter(upgrade => upgrade.pcId === pc.id);
                const upgradeStatus = pendingUpgrades.length > 0 ? 
                    `<span class="upgrade-status status-pending">${pendingUpgrades.length} pending upgrade(s)</span>` : '';
                
                pcDiv.innerHTML = `
                    <div class="item-header">
                        <div class="item-name">${pc.pcName || 'Unnamed PC'} ${pc.category ? `<span style="background: #007bff; color: white; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px;">${pc.category}</span>` : ''}${upgradeStatus}</div>
                        <div>
                            <button class="upgrade-btn" onclick="requestUpgradeForPC(${originalIndex})">Request Upgrade</button>
                            <button class="btn btn-secondary" onclick="editPC(${originalIndex})" style="margin-right: 5px;">Edit</button>
                            <button class="delete-btn" onclick="deletePC(${originalIndex})">Delete</button>
                        </div>
                    </div>
                    <div class="item-details">
                        ${pc.processor ? `<div><strong>Processor:</strong> ${pc.processor}</div>` : ''}
                        ${pc.ram ? `<div><strong>RAM:</strong> ${pc.ram}</div>` : ''}
                        ${pc.storage ? `<div><strong>Storage:</strong> ${pc.storage}</div>` : ''}
                        ${pc.gpu ? `<div><strong>GPU:</strong> ${pc.gpu}</div>` : ''}
                        ${pc.motherboard ? `<div><strong>Motherboard:</strong> ${pc.motherboard}</div>` : ''}
                        ${pc.powerSupply ? `<div><strong>Power Supply:</strong> ${pc.powerSupply}</div>` : ''}
                    </div>
                    ${pc.notes ? `<div class="item-specs"><strong>Notes:</strong> ${pc.notes}</div>` : ''}
                `;
                pcList.appendChild(pcDiv);
            });
        }

        function renderUpgrades() {
            const upgradeList = document.getElementById('upgradeList');
            upgradeList.innerHTML = '';
            
            upgrades.forEach((upgrade, index) => {
                // Find PC by ID instead of index
                const pc = pcs.find(p => p.id === upgrade.pcId);
                if (!pc) return; // Skip if PC was deleted
                
                const upgradeDiv = document.createElement('div');
                upgradeDiv.className = 'item';
                upgradeDiv.innerHTML = `
                    <div class="item-header">
                        <div class="item-name">Upgrade Request: ${pc.pcName}</div>
                        <button class="delete-btn" onclick="deleteUpgrade(${index})">Delete</button>
                    </div>
                    <div class="item-details">
                        <div><strong>PC:</strong> ${pc.pcName}</div>
                        <div><strong>Part to Upgrade:</strong> ${upgrade.part}</div>
                        <div><strong>Current Spec:</strong> ${pc[upgrade.part] || 'N/A'}</div>
                    </div>
                    <div class="item-specs">
                        <strong>Reason:</strong> ${upgrade.reason}
                    </div>
                `;
                upgradeList.appendChild(upgradeDiv);
            });
        }

        function requestUpgradeForPC(pcIndex) {
            showTab('upgrades');
            document.getElementById('selectPC').value = pcIndex;
            showSelectedPCSpecs(pcIndex); // Show specs for the selected PC
        }

        function deleteUpgrade(index) {
            if (confirm('Are you sure you want to delete this upgrade request?')) {
                const upgradeId = upgrades[index].id;
                deleteUpgradeFromAPI(upgradeId);
            }
        }

        function clearAllPCs() {
            if (confirm('Are you sure you want to clear all PC data? This will also remove all upgrade requests.')) {
                // Delete all PCs via API
                Promise.all(pcs.map(pc => deletePCFromAPI(pc.id)))
                    .then(() => {
                        pcs = [];
                        upgrades = [];
                        renderPCs();
                        renderUpgrades();
                        updatePCDropdown();
                    });
            }
        }

        function clearAllUpgrades() {
            if (confirm('Are you sure you want to clear all upgrade requests?')) {
                // Delete all upgrades via API
                Promise.all(upgrades.map(upgrade => deleteUpgradeFromAPI(upgrade.id)))
                    .then(() => {
                        upgrades = [];
                        renderUpgrades();
                        renderPCs();
                    });
            }
        }

        // Edit PC functions
        let editingPCIndex = -1;

        function editPC(index) {
            editingPCIndex = index;
            const pc = pcs[index];

            document.getElementById('editPcName').value = pc.pcName;
            document.getElementById('editProcessor').value = pc.processor;
            document.getElementById('editRam').value = pc.ram;
            document.getElementById('editStorage').value = pc.storage;
            document.getElementById('editGpu').value = pc.gpu || '';
            document.getElementById('editMotherboard').value = pc.motherboard || '';
            document.getElementById('editPowerSupply').value = pc.powerSupply || '';
            document.getElementById('editCategory').value = pc.category || '';
            document.getElementById('editNotes').value = pc.notes || '';

            document.getElementById('editModal').style.display = 'block';
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            editingPCIndex = -1;
        }

        async function saveEditedPC() {
            if (editingPCIndex === -1) return;

            const pc = pcs[editingPCIndex];
            const updatedPC = {
                id: pc.id,
                pcName: document.getElementById('editPcName').value.trim(),
                processor: document.getElementById('editProcessor').value.trim(),
                ram: document.getElementById('editRam').value.trim(),
                storage: document.getElementById('editStorage').value.trim(),
                gpu: document.getElementById('editGpu').value.trim(),
                motherboard: document.getElementById('editMotherboard').value.trim(),
                powerSupply: document.getElementById('editPowerSupply').value.trim(),
                category: document.getElementById('editCategory').value,
                notes: document.getElementById('editNotes').value.trim()
            };

            if (updatedPC.pcName || updatedPC.processor || updatedPC.ram || updatedPC.storage || updatedPC.gpu || updatedPC.motherboard || updatedPC.powerSupply || updatedPC.notes || updatedPC.category) {
                try {
                    const response = await fetch(`${API_BASE}/update-pc`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(updatedPC)
                    });

                    if (response.ok) {
                        // Update local array
                        pcs[editingPCIndex] = { ...pc, ...updatedPC };
                        renderPCs();
                        renderUpgrades(); // Refresh upgrade requests to show updated PC name
                        updatePCDropdown();
                        closeEditModal();
                    } else {
                        alert('Error updating PC. Please try again.');
                    }
                } catch (error) {
                    console.error('Error updating PC:', error);
                    alert('Error updating PC. Please try again.');
                }
            } else {
                alert('Please fill in at least one field');
            }
        }

        // Dashboard functions
        let dashboardData = {
            totalPCs: 0,
            pendingUpgrades: 0,
            pendingEquipment: 0,
            completedTodos: 0,
            totalTodos: 0,
            categories: { Admin: 0, Graphic: 0, EMP: 0 },
            recentActivity: []
        };

        async function updateDashboard() {
            await calculateDashboardStats();
            renderDashboardStats();
            renderCategoryBreakdown();
            renderRecentActivity();
        }

        async function calculateDashboardStats() {
            // Calculate PC stats
            dashboardData.totalPCs = pcs.length;
            dashboardData.pendingUpgrades = upgrades.length;
            dashboardData.pendingEquipment = equipment.filter(item => item.status === 'Pending').length;

            // Calculate category breakdown
            dashboardData.categories = { Admin: 0, Graphic: 0, EMP: 0 };
            pcs.forEach(pc => {
                if (pc.category && dashboardData.categories.hasOwnProperty(pc.category)) {
                    dashboardData.categories[pc.category]++;
                }
            });

            // Fetch todo stats
            try {
                const response = await fetch(`${API_BASE}/get-todos`);
                if (response.ok) {
                    const todos = await response.json();
                    dashboardData.totalTodos = todos.length;
                    dashboardData.completedTodos = todos.filter(todo => todo.completed).length;
                }
            } catch (error) {
                console.error('Error fetching todos:', error);
                dashboardData.totalTodos = 0;
                dashboardData.completedTodos = 0;
            }

            // Generate recent activity
            generateRecentActivity();
        }

        function renderDashboardStats() {
            document.getElementById('totalPCs').textContent = dashboardData.totalPCs;
            document.getElementById('pendingUpgrades').textContent = dashboardData.pendingUpgrades;
            document.getElementById('pendingEquipment').textContent = dashboardData.pendingEquipment;
            document.getElementById('completedTodos').textContent = dashboardData.completedTodos;
            document.getElementById('totalTodos').textContent = dashboardData.totalTodos;
        }

        function renderCategoryBreakdown() {
            const total = dashboardData.totalPCs;

            Object.keys(dashboardData.categories).forEach(category => {
                const count = dashboardData.categories[category];
                const percentage = total > 0 ? (count / total) * 100 : 0;

                const countElement = document.getElementById(`${category.toLowerCase()}Count`);
                const progressElement = document.getElementById(`${category.toLowerCase()}Progress`);

                if (countElement) countElement.textContent = count;
                if (progressElement) progressElement.style.width = `${percentage}%`;
            });
        }

        function generateRecentActivity() {
            dashboardData.recentActivity = [];

            // Add recent PCs
            const recentPCs = pcs.slice(-3).reverse();
            recentPCs.forEach(pc => {
                dashboardData.recentActivity.push({
                    icon: '💻',
                    text: `Added PC: ${pc.pcName} (${pc.category})`,
                    time: getRelativeTime(pc.createdAt)
                });
            });

            // Add recent upgrades
            const recentUpgrades = upgrades.slice(-2).reverse();
            recentUpgrades.forEach(upgrade => {
                const pc = pcs.find(p => p.id === upgrade.pcId);
                dashboardData.recentActivity.push({
                    icon: '⚡',
                    text: `Upgrade requested for ${pc ? pc.pcName : 'Unknown PC'}`,
                    time: getRelativeTime(upgrade.date)
                });
            });

            // Add recent equipment requests
            const recentEquipment = equipment.slice(-2).reverse();
            recentEquipment.forEach(item => {
                dashboardData.recentActivity.push({
                    icon: '🛒',
                    text: `Equipment requested: ${item.name} (${item.type})`,
                    time: getRelativeTime(item.createdAt)
                });
            });

            // Sort by most recent
            dashboardData.recentActivity.sort((a, b) => new Date(b.time) - new Date(a.time));
            dashboardData.recentActivity = dashboardData.recentActivity.slice(0, 5);
        }

        function renderRecentActivity() {
            const activityFeed = document.getElementById('activityFeed');

            if (dashboardData.recentActivity.length === 0) {
                activityFeed.innerHTML = `
                    <div class="activity-item">
                        <div class="activity-icon">📊</div>
                        <div class="activity-content">
                            <div class="activity-text">No recent activity</div>
                            <div class="activity-time">Start by adding PCs or creating tasks</div>
                        </div>
                    </div>
                `;
                return;
            }

            activityFeed.innerHTML = dashboardData.recentActivity.map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">${activity.icon}</div>
                    <div class="activity-content">
                        <div class="activity-text">${activity.text}</div>
                        <div class="activity-time">${activity.time}</div>
                    </div>
                </div>
            `).join('');
        }

        function getRelativeTime(dateString) {
            if (!dateString) return 'Unknown time';

            const date = new Date(dateString);
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            if (diffMins < 1) return 'Just now';
            if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`;
            if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
            if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
            return date.toLocaleDateString();
        }

        function refreshDashboard() {
            updateDashboard();

            // Show refresh feedback
            const refreshBtn = event.target;
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<span class="action-icon">⏳</span>Refreshing...';
            refreshBtn.disabled = true;

            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;
            }, 1000);
        }

        // Event listeners
        document.getElementById('addPcBtn').addEventListener('click', addPC);
        document.getElementById('addUpgradeBtn').addEventListener('click', addUpgrade);
        document.getElementById('addEquipmentBtn').addEventListener('click', addEquipment);
    </script>
</body>
</html>





