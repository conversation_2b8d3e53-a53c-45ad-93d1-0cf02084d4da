const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        
        // Parse request parameters
        const params = new URLSearchParams(event.queryString || '');
        const mode = params.get('mode') || 'auto'; // auto, manual, preview
        const archiveDate = params.get('date') || new Date().toISOString().split('T')[0];
        const strategy = params.get('strategy') || 'completed'; // completed, all, selective
        
        let result;
        
        switch (mode) {
            case 'preview':
                result = await previewArchival(db, archiveDate, strategy);
                break;
            case 'manual':
                result = await performArchival(db, archiveDate, strategy, true);
                break;
            case 'auto':
            default:
                result = await performArchival(db, archiveDate, strategy, false);
                break;
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result)
        };

    } catch (error) {
        console.error('Error in daily archival:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to perform daily archival',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

async function performArchival(db, archiveDate, strategy, isManual) {
    const todosCollection = db.collection('todos');
    const archiveCollection = db.collection('archived_tasks');
    const archiveLogCollection = db.collection('archive_log');
    
    // Get tasks to archive based on strategy
    const tasksToArchive = await getTasksToArchive(todosCollection, archiveDate, strategy);
    
    if (tasksToArchive.length === 0) {
        return {
            success: true,
            message: 'No tasks to archive',
            archived: 0,
            remaining: await todosCollection.countDocuments(),
            date: archiveDate,
            strategy: strategy
        };
    }
    
    // Prepare archived tasks with metadata
    const archivedTasks = tasksToArchive.map(task => ({
        ...task,
        originalId: task._id,
        archivedAt: new Date().toISOString(),
        archiveDate: archiveDate,
        archiveReason: getArchiveReason(task, strategy),
        isManualArchive: isManual,
        dayOfWeek: new Date(archiveDate).toLocaleDateString('en-US', { weekday: 'long' }),
        weekNumber: getWeekNumber(new Date(archiveDate)),
        monthYear: new Date(archiveDate).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
    }));
    
    // Calculate archival statistics
    const stats = calculateArchivalStats(archivedTasks);
    
    // Perform the archival transaction
    const session = client.startSession();
    
    try {
        await session.withTransaction(async () => {
            // Insert into archive
            await archiveCollection.insertMany(archivedTasks, { session });
            
            // Remove from active todos
            const taskIds = tasksToArchive.map(task => task._id);
            await todosCollection.deleteMany({ _id: { $in: taskIds } }, { session });
            
            // Log the archival operation
            await archiveLogCollection.insertOne({
                date: archiveDate,
                timestamp: new Date().toISOString(),
                strategy: strategy,
                isManual: isManual,
                tasksArchived: archivedTasks.length,
                statistics: stats,
                taskIds: taskIds.map(id => id.toString())
            }, { session });
        });
        
        return {
            success: true,
            message: `Successfully archived ${archivedTasks.length} tasks`,
            archived: archivedTasks.length,
            remaining: await todosCollection.countDocuments(),
            date: archiveDate,
            strategy: strategy,
            statistics: stats,
            isManual: isManual
        };
        
    } catch (error) {
        throw new Error(`Transaction failed: ${error.message}`);
    } finally {
        await session.endSession();
    }
}

async function previewArchival(db, archiveDate, strategy) {
    const todosCollection = db.collection('todos');
    const tasksToArchive = await getTasksToArchive(todosCollection, archiveDate, strategy);
    
    const stats = calculateArchivalStats(tasksToArchive);
    const remaining = await todosCollection.countDocuments() - tasksToArchive.length;
    
    return {
        preview: true,
        date: archiveDate,
        strategy: strategy,
        tasksToArchive: tasksToArchive.length,
        tasksRemaining: remaining,
        statistics: stats,
        tasks: tasksToArchive.map(task => ({
            id: task._id.toString(),
            task: task.task,
            completed: task.completed,
            createdAt: task.createdAt,
            archiveReason: getArchiveReason(task, strategy)
        }))
    };
}

async function getTasksToArchive(collection, archiveDate, strategy) {
    const query = buildArchivalQuery(archiveDate, strategy);
    return await collection.find(query).toArray();
}

function buildArchivalQuery(archiveDate, strategy) {
    const archiveDateObj = new Date(archiveDate);
    const startOfDay = new Date(archiveDateObj);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(archiveDateObj);
    endOfDay.setHours(23, 59, 59, 999);
    
    switch (strategy) {
        case 'completed':
            // Archive only completed tasks from the specified date
            return {
                completed: true,
                $or: [
                    { completedAt: { $gte: startOfDay.toISOString(), $lte: endOfDay.toISOString() } },
                    { 
                        createdAt: { $gte: startOfDay.toISOString(), $lte: endOfDay.toISOString() },
                        completed: true
                    }
                ]
            };
            
        case 'all':
            // Archive all tasks from the specified date
            return {
                createdAt: { $gte: startOfDay.toISOString(), $lte: endOfDay.toISOString() }
            };
            
        case 'selective':
            // Archive completed tasks and old incomplete tasks (older than 3 days)
            const threeDaysAgo = new Date(archiveDateObj);
            threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);
            
            return {
                $or: [
                    { completed: true },
                    { 
                        completed: false,
                        createdAt: { $lt: threeDaysAgo.toISOString() }
                    }
                ]
            };
            
        default:
            return { completed: true };
    }
}

function getArchiveReason(task, strategy) {
    if (task.completed) {
        return 'Task completed';
    }
    
    switch (strategy) {
        case 'all':
            return 'Daily archival - all tasks';
        case 'selective':
            return 'Task aged out (incomplete for >3 days)';
        default:
            return 'Daily archival';
    }
}

function calculateArchivalStats(tasks) {
    const stats = {
        total: tasks.length,
        completed: 0,
        incomplete: 0,
        withProblem: 0,
        withSolution: 0,
        categories: {
            'Technical Issues': 0,
            'Maintenance': 0,
            'User Support': 0,
            'System Updates': 0,
            'Other': 0
        },
        averageAge: 0,
        oldestTask: null,
        newestTask: null
    };
    
    if (tasks.length === 0) return stats;
    
    let totalAge = 0;
    let oldestDate = new Date();
    let newestDate = new Date(0);
    
    tasks.forEach(task => {
        // Completion status
        if (task.completed) {
            stats.completed++;
        } else {
            stats.incomplete++;
        }
        
        // Content analysis
        if (task.problem && task.problem.trim()) {
            stats.withProblem++;
        }
        if (task.impact && task.impact.trim()) {
            stats.withSolution++;
        }
        
        // Categorization
        const category = categorizeTask(task);
        stats.categories[category]++;
        
        // Age calculation
        if (task.createdAt) {
            const createdDate = new Date(task.createdAt);
            const age = (new Date() - createdDate) / (1000 * 60 * 60 * 24); // days
            totalAge += age;
            
            if (createdDate < oldestDate) {
                oldestDate = createdDate;
                stats.oldestTask = task.task || 'Untitled';
            }
            if (createdDate > newestDate) {
                newestDate = createdDate;
                stats.newestTask = task.task || 'Untitled';
            }
        }
    });
    
    stats.averageAge = Math.round((totalAge / tasks.length) * 10) / 10;
    stats.completionRate = Math.round((stats.completed / stats.total) * 100);
    
    return stats;
}

function categorizeTask(task) {
    const content = ((task.task || '') + ' ' + (task.problem || '') + ' ' + (task.impact || '')).toLowerCase();
    
    if (content.includes('technical') || content.includes('bug') || content.includes('error')) {
        return 'Technical Issues';
    } else if (content.includes('maintenance') || content.includes('update') || content.includes('patch')) {
        return 'Maintenance';
    } else if (content.includes('user') || content.includes('support') || content.includes('help')) {
        return 'User Support';
    } else if (content.includes('system') || content.includes('server') || content.includes('network')) {
        return 'System Updates';
    } else {
        return 'Other';
    }
}

function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}
