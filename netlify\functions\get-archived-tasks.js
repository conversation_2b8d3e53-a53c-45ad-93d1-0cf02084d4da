const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        
        // Parse query parameters
        const params = new URLSearchParams(event.queryString || '');
        const dateFrom = params.get('dateFrom');
        const dateTo = params.get('dateTo');
        const period = params.get('period') || '30d';
        const category = params.get('category');
        const completed = params.get('completed');
        const limit = parseInt(params.get('limit')) || 100;
        const page = parseInt(params.get('page')) || 1;
        const sortBy = params.get('sortBy') || 'archivedAt';
        const sortOrder = params.get('sortOrder') || 'desc';
        const includeStats = params.get('includeStats') === 'true';

        // Build query
        const query = buildArchiveQuery(dateFrom, dateTo, period, category, completed);
        
        // Get archived tasks
        const archiveCollection = db.collection('archived_tasks');
        
        // Calculate pagination
        const skip = (page - 1) * limit;
        
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;
        
        // Execute queries
        const [tasks, totalCount] = await Promise.all([
            archiveCollection
                .find(query)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .toArray(),
            archiveCollection.countDocuments(query)
        ]);

        // Format tasks for response
        const formattedTasks = tasks.map(task => ({
            id: task._id.toString(),
            originalId: task.originalId?.toString(),
            task: task.task,
            problem: task.problem,
            impact: task.impact,
            completed: task.completed,
            createdAt: task.createdAt,
            completedAt: task.completedAt,
            archivedAt: task.archivedAt,
            archiveDate: task.archiveDate,
            archiveReason: task.archiveReason,
            isManualArchive: task.isManualArchive,
            dayOfWeek: task.dayOfWeek,
            weekNumber: task.weekNumber,
            monthYear: task.monthYear,
            category: categorizeTask(task)
        }));

        let result = {
            tasks: formattedTasks,
            pagination: {
                page: page,
                limit: limit,
                total: totalCount,
                pages: Math.ceil(totalCount / limit),
                hasNext: page < Math.ceil(totalCount / limit),
                hasPrev: page > 1
            },
            query: {
                dateFrom,
                dateTo,
                period,
                category,
                completed,
                sortBy,
                sortOrder
            }
        };

        // Include statistics if requested
        if (includeStats) {
            result.statistics = await generateArchiveStatistics(archiveCollection, query);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result)
        };

    } catch (error) {
        console.error('Error fetching archived tasks:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to fetch archived tasks',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

function buildArchiveQuery(dateFrom, dateTo, period, category, completed) {
    let query = {};

    // Date filtering
    if (dateFrom && dateTo) {
        query.archiveDate = {
            $gte: dateFrom,
            $lte: dateTo
        };
    } else if (period) {
        const endDate = new Date();
        const startDate = getPeriodStartDate(endDate, period);
        query.archiveDate = {
            $gte: startDate.toISOString().split('T')[0],
            $lte: endDate.toISOString().split('T')[0]
        };
    }

    // Category filtering
    if (category && category !== 'all') {
        // We'll need to filter by content since category is calculated
        const categoryKeywords = getCategoryKeywords(category);
        if (categoryKeywords.length > 0) {
            query.$or = [
                { task: { $regex: categoryKeywords.join('|'), $options: 'i' } },
                { problem: { $regex: categoryKeywords.join('|'), $options: 'i' } },
                { impact: { $regex: categoryKeywords.join('|'), $options: 'i' } }
            ];
        }
    }

    // Completion status filtering
    if (completed !== null && completed !== undefined) {
        query.completed = completed === 'true';
    }

    return query;
}

function getPeriodStartDate(endDate, period) {
    const start = new Date(endDate);
    switch (period) {
        case '7d':
            start.setDate(start.getDate() - 7);
            break;
        case '30d':
            start.setDate(start.getDate() - 30);
            break;
        case '90d':
            start.setDate(start.getDate() - 90);
            break;
        case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
        default:
            start.setDate(start.getDate() - 30);
    }
    return start;
}

function getCategoryKeywords(category) {
    const keywords = {
        'Technical Issues': ['technical', 'bug', 'error', 'issue', 'problem', 'fix'],
        'Maintenance': ['maintenance', 'update', 'patch', 'upgrade', 'install'],
        'User Support': ['user', 'support', 'help', 'assist', 'training'],
        'System Updates': ['system', 'server', 'network', 'infrastructure'],
        'Other': []
    };
    return keywords[category] || [];
}

function categorizeTask(task) {
    const content = ((task.task || '') + ' ' + (task.problem || '') + ' ' + (task.impact || '')).toLowerCase();
    
    if (content.includes('technical') || content.includes('bug') || content.includes('error')) {
        return 'Technical Issues';
    } else if (content.includes('maintenance') || content.includes('update') || content.includes('patch')) {
        return 'Maintenance';
    } else if (content.includes('user') || content.includes('support') || content.includes('help')) {
        return 'User Support';
    } else if (content.includes('system') || content.includes('server') || content.includes('network')) {
        return 'System Updates';
    } else {
        return 'Other';
    }
}

async function generateArchiveStatistics(collection, query) {
    const pipeline = [
        { $match: query },
        {
            $group: {
                _id: null,
                totalTasks: { $sum: 1 },
                completedTasks: {
                    $sum: { $cond: [{ $eq: ["$completed", true] }, 1, 0] }
                },
                incompleteTasks: {
                    $sum: { $cond: [{ $eq: ["$completed", false] }, 1, 0] }
                },
                manualArchives: {
                    $sum: { $cond: [{ $eq: ["$isManualArchive", true] }, 1, 0] }
                },
                autoArchives: {
                    $sum: { $cond: [{ $eq: ["$isManualArchive", false] }, 1, 0] }
                },
                tasksWithProblem: {
                    $sum: { $cond: [{ $ne: ["$problem", ""] }, 1, 0] }
                },
                tasksWithSolution: {
                    $sum: { $cond: [{ $ne: ["$impact", ""] }, 1, 0] }
                }
            }
        }
    ];

    const [stats] = await collection.aggregate(pipeline).toArray();
    
    if (!stats) {
        return {
            totalTasks: 0,
            completedTasks: 0,
            incompleteTasks: 0,
            completionRate: 0,
            manualArchives: 0,
            autoArchives: 0,
            tasksWithProblem: 0,
            tasksWithSolution: 0,
            qualityScore: 0
        };
    }

    // Calculate derived metrics
    const completionRate = stats.totalTasks > 0 
        ? Math.round((stats.completedTasks / stats.totalTasks) * 100) 
        : 0;
    
    const qualityScore = stats.totalTasks > 0
        ? Math.round(((stats.tasksWithProblem + stats.tasksWithSolution) / (stats.totalTasks * 2)) * 100)
        : 0;

    return {
        ...stats,
        completionRate,
        qualityScore
    };
}

// Additional utility functions for archive management
async function getArchiveLog(db, dateFrom, dateTo) {
    const logCollection = db.collection('archive_log');
    
    let query = {};
    if (dateFrom && dateTo) {
        query.date = { $gte: dateFrom, $lte: dateTo };
    }
    
    return await logCollection
        .find(query)
        .sort({ timestamp: -1 })
        .toArray();
}

async function getArchiveSummary(db, period = '30d') {
    const archiveCollection = db.collection('archived_tasks');
    const endDate = new Date();
    const startDate = getPeriodStartDate(endDate, period);
    
    const pipeline = [
        {
            $match: {
                archiveDate: {
                    $gte: startDate.toISOString().split('T')[0],
                    $lte: endDate.toISOString().split('T')[0]
                }
            }
        },
        {
            $group: {
                _id: "$archiveDate",
                totalArchived: { $sum: 1 },
                completedArchived: {
                    $sum: { $cond: [{ $eq: ["$completed", true] }, 1, 0] }
                },
                manualArchived: {
                    $sum: { $cond: [{ $eq: ["$isManualArchive", true] }, 1, 0] }
                }
            }
        },
        { $sort: { _id: 1 } }
    ];
    
    return await archiveCollection.aggregate(pipeline).toArray();
}
