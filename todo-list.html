<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily To-Do List - Work Management System</title>

    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for todo list page */
        body {
            background: linear-gradient(135deg, var(--secondary-50) 0%, var(--secondary-100) 100%);
            min-height: 100vh;
            padding: var(--space-4);
        }

        .page-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .page-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
        }

        .page-title i {
            color: var(--success-600);
        }

        .page-date {
            font-size: var(--font-size-lg);
            color: var(--secondary-600);
            font-weight: var(--font-weight-medium);
        }

        .back-link {
            position: absolute;
            top: var(--space-4);
            left: var(--space-4);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--secondary-600);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: color var(--transition-fast);
        }

        .back-link:hover {
            color: var(--primary-600);
        }

        .add-task-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-md);
            margin-bottom: var(--space-8);
            border: 1px solid var(--secondary-200);
        }

        .add-task-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }

        .add-task-header i {
            color: var(--primary-600);
            font-size: var(--font-size-xl);
        }

        .add-task-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
        }

        .form-hint {
            background: var(--primary-50);
            border: 1px solid var(--primary-200);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            margin-bottom: var(--space-6);
            color: var(--primary-700);
            font-size: var(--font-size-sm);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .form-hint i {
            color: var(--primary-600);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-6);
            margin-bottom: var(--space-6);
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: var(--space-4);
            }
        }

        .task-list-container {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
            overflow: hidden;
        }

        .task-list-header {
            background: var(--secondary-50);
            padding: var(--space-6);
            border-bottom: 1px solid var(--secondary-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .task-list-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .task-count {
            background: var(--primary-100);
            color: var(--primary-700);
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }

        .task-item {
            display: flex;
            align-items: flex-start;
            padding: var(--space-6);
            border-bottom: 1px solid var(--secondary-200);
            transition: all var(--transition-fast);
            position: relative;
        }

        .task-item:last-child {
            border-bottom: none;
        }

        .task-item:hover {
            background: var(--secondary-50);
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            margin-right: var(--space-4);
            cursor: pointer;
            accent-color: var(--success-600);
        }

        .task-content {
            flex: 1;
            min-width: 0;
        }

        .task-title {
            font-weight: var(--font-weight-semibold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            font-size: var(--font-size-base);
        }

        .task-details {
            display: grid;
            gap: var(--space-3);
            font-size: var(--font-size-sm);
            color: var(--secondary-600);
        }

        .task-detail {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .task-detail-label {
            font-weight: var(--font-weight-medium);
            color: var(--secondary-700);
            font-size: var(--font-size-xs);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .task-actions {
            display: flex;
            gap: var(--space-2);
            margin-left: var(--space-4);
        }

        .task-delete-btn {
            background: var(--error-500);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-size-xs);
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .task-delete-btn:hover {
            background: var(--error-600);
            transform: translateY(-1px);
        }

        .completed .task-title,
        .completed .task-details {
            text-decoration: line-through;
            opacity: 0.6;
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: var(--space-4);
            margin: var(--space-8) 0;
            flex-wrap: wrap;
        }

        .signature-section {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
            margin-top: var(--space-8);
        }

        .signature-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .signature-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
        }

        .signature-subtitle {
            color: var(--secondary-600);
            font-size: var(--font-size-sm);
        }

        .signature-line {
            display: flex;
            align-items: center;
            gap: var(--space-4);
            margin-bottom: var(--space-8);
            padding: var(--space-4);
            background: var(--secondary-50);
            border-radius: var(--radius-lg);
        }

        .signature-label {
            font-weight: var(--font-weight-medium);
            color: var(--secondary-700);
            min-width: 140px;
        }

        .signature-box {
            border-bottom: 2px solid var(--secondary-400);
            width: 200px;
            height: 40px;
            background: white;
            border-radius: var(--radius-sm) var(--radius-sm) 0 0;
        }

        .date-box {
            border-bottom: 2px solid var(--secondary-400);
            width: 120px;
            height: 40px;
            background: white;
            border-radius: var(--radius-sm) var(--radius-sm) 0 0;
        }

        .empty-state {
            text-align: center;
            padding: var(--space-16);
            color: var(--secondary-500);
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: var(--space-4);
            color: var(--secondary-400);
        }

        .empty-state h3 {
            font-size: var(--font-size-xl);
            margin-bottom: var(--space-2);
            color: var(--secondary-600);
        }

        .empty-state p {
            font-size: var(--font-size-base);
            margin: 0;
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                margin: 0;
                padding: 0;
            }

            .container {
                box-shadow: none;
                padding: 20px;
            }

            .add-task, .controls, .delete-btn {
                display: none !important;
            }

            .task-item {
                border-bottom: 1px solid #ccc;
                page-break-inside: avoid;
            }

            .task-checkbox {
                -webkit-appearance: none;
                appearance: none;
                width: 18px;
                height: 18px;
                border: 2px solid #333;
                margin-right: 15px;
                position: relative;
            }

            .task-checkbox:checked::after {
                content: '✓';
                position: absolute;
                top: -2px;
                left: 2px;
                font-size: 14px;
                font-weight: bold;
            }

            h1 {
                font-size: 24px;
            }

            .date {
                font-size: 16px;
                margin-bottom: 20px;
            }

            .task-details {
                margin-left: 35px;
                margin-top: 10px;
                font-size: 12px;
                line-height: 1.4;
            }

            .task-details div {
                margin-bottom: 6px;
                page-break-inside: avoid;
            }

            .task-header {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
            }

            .header-info {
                background: white;
                border: 1px solid #333;
                margin-bottom: 20px;
            }

            .header-info input {
                border: none;
                background: transparent;
                font-weight: bold;
                font-size: 16px;
            }

            .signature-section {
                page-break-inside: avoid;
                margin-top: 40px;
                border-top: 2px solid #333;
            }

            .signature-line {
                display: flex;
                align-items: center;
                gap: 15px;
                margin-top: 30px;
                font-weight: bold;
            }

            .signature-box, .date-box {
                border-bottom: 2px solid #333;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Dashboard
    </a>

    <div class="page-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                Daily To-Do List
            </h1>
            <div class="page-date" id="currentDate"></div>
        </div>

        <div class="add-task-card">
            <div class="add-task-header">
                <i class="fas fa-plus-circle"></i>
                <h2 class="add-task-title">Add New Task</h2>
            </div>

            <div class="form-hint">
                <i class="fas fa-info-circle"></i>
                Fill in any fields that are relevant to your task. All fields are optional.
            </div>

            <div class="form-group">
                <label for="taskInput" class="form-label">Task/Issue Title (optional):</label>
                <input type="text" id="taskInput" class="form-input" placeholder="Brief description of the task or issue..." maxlength="100">
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="problemDesc" class="form-label">Problem Description (optional):</label>
                    <textarea id="problemDesc" class="form-textarea" placeholder="Detailed description of the problem or task requirements..."></textarea>
                </div>
                <div class="form-group">
                    <label for="solutionDesc" class="form-label">Solution/Fix Applied (optional):</label>
                    <textarea id="solutionDesc" class="form-textarea" placeholder="How you solved the problem or completed the task..."></textarea>
                </div>
            </div>

            <div class="form-group">
                <label for="performanceImpact" class="form-label">Performance Impact (optional):</label>
                <textarea id="performanceImpact" class="form-textarea" placeholder="How the fix affected performance, efficiency, or outcomes..."></textarea>
            </div>

            <button id="addBtn" class="btn btn-primary btn-lg" style="width: 100%;">
                <i class="fas fa-plus"></i>
                Add Task
            </button>
        </div>

        <div class="controls">
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print"></i>
                Print List
            </button>
            <button class="btn btn-primary" onclick="archiveCompleted()">
                <i class="fas fa-archive"></i>
                Archive Completed
            </button>
            <button class="btn btn-secondary" onclick="viewArchivedTasks()">
                <i class="fas fa-history"></i>
                View Archived
            </button>
            <button class="btn btn-secondary" onclick="clearCompleted()">
                <i class="fas fa-check-double"></i>
                Clear Completed
            </button>
            <button class="btn btn-warning" onclick="clearAll()">
                <i class="fas fa-trash-alt"></i>
                Clear All
            </button>
        </div>

        <!-- Archival Status Info -->
        <div class="card" style="margin-bottom: var(--space-6); background: var(--primary-50); border-color: var(--primary-200);">
            <div class="card-body">
                <div style="display: flex; align-items: center; gap: var(--space-3);">
                    <i class="fas fa-info-circle" style="color: var(--primary-600); font-size: var(--font-size-lg);"></i>
                    <div>
                        <strong style="color: var(--primary-800);">Daily Task Archival:</strong>
                        <span id="archivalStatus" style="color: var(--primary-700);">Loading status...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="task-list-container">
            <div class="task-list-header">
                <h3 class="task-list-title">
                    <i class="fas fa-tasks"></i>
                    Today's Tasks
                </h3>
                <span class="task-count" id="taskCount">0 tasks</span>
            </div>
            <div id="taskList"></div>
        </div>

        <div class="signature-section">
            <div class="signature-header">
                <h3 class="signature-title">Approval Signatures</h3>
                <p class="signature-subtitle">Required signatures for task completion verification</p>
            </div>

            <div class="signature-line">
                <span class="signature-label">Employee Signature:</span>
                <div class="signature-box"></div>
                <span class="signature-label">Date:</span>
                <div class="date-box"></div>
            </div>
            <div class="signature-line">
                <span class="signature-label">Supervisor Signature:</span>
                <div class="signature-box"></div>
                <span class="signature-label">Date:</span>
                <div class="date-box"></div>
            </div>
        </div>
    </div>

    <script>
        // Set current date
        document.getElementById('currentDate').textContent = 
            new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });

        let tasks = [];
        const API_BASE = '/.netlify/functions';

        // Load tasks from database
        async function loadTasks() {
            try {
                const response = await fetch(`${API_BASE}/get-todos`);
                if (response.ok) {
                    tasks = await response.json();
                    renderTasks();
                }
            } catch (error) {
                console.error('Error loading tasks:', error);
            }
        }

        // Save task to database
        async function saveTask(task) {
            try {
                const response = await fetch(`${API_BASE}/add-todo`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(task)
                });

                if (response.ok) {
                    const newTask = await response.json();
                    tasks.push(newTask);
                    renderTasks();
                    return newTask;
                }
            } catch (error) {
                console.error('Error saving task:', error);
                alert('Error saving task. Please try again.');
            }
        }

        // Update task in database
        async function updateTask(taskId, updates) {
            try {
                const response = await fetch(`${API_BASE}/update-todo`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: taskId, ...updates })
                });

                if (response.ok) {
                    // Update local array
                    const taskIndex = tasks.findIndex(t => t.id === taskId);
                    if (taskIndex !== -1) {
                        tasks[taskIndex] = { ...tasks[taskIndex], ...updates };
                        renderTasks();
                    }
                }
            } catch (error) {
                console.error('Error updating task:', error);
                alert('Error updating task. Please try again.');
            }
        }

        // Delete task from database
        async function deleteTask(taskId) {
            try {
                const response = await fetch(`${API_BASE}/delete-todo`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: taskId })
                });

                if (response.ok) {
                    // Remove from local array
                    tasks = tasks.filter(task => task.id !== taskId);
                    renderTasks();
                }
            } catch (error) {
                console.error('Error deleting task:', error);
                alert('Error deleting task. Please try again.');
            }
        }

        function renderTasks() {
            const taskList = document.getElementById('taskList');
            const taskCount = document.getElementById('taskCount');

            // Update task count
            taskCount.textContent = `${tasks.length} task${tasks.length !== 1 ? 's' : ''}`;

            if (tasks.length === 0) {
                taskList.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-clipboard-list"></i>
                        <h3>No tasks yet</h3>
                        <p>Add your first task above to get started!</p>
                    </div>
                `;
                return;
            }

            taskList.innerHTML = '';

            tasks.forEach((task, index) => {
                const taskItem = document.createElement('div');
                taskItem.className = `task-item ${task.completed ? 'completed' : ''}`;

                // Build task details HTML
                let taskDetailsHTML = '';
                const details = [];

                if (task.problem) {
                    details.push(`
                        <div class="task-detail">
                            <span class="task-detail-label">Problem Description</span>
                            <span>${task.problem}</span>
                        </div>
                    `);
                }

                if (task.impact) {
                    // Split combined solution and impact if it contains ' | '
                    const parts = task.impact.split(' | ');
                    if (parts.length > 1) {
                        details.push(`
                            <div class="task-detail">
                                <span class="task-detail-label">Solution Applied</span>
                                <span>${parts[0]}</span>
                            </div>
                        `);
                        details.push(`
                            <div class="task-detail">
                                <span class="task-detail-label">Performance Impact</span>
                                <span>${parts[1]}</span>
                            </div>
                        `);
                    } else {
                        details.push(`
                            <div class="task-detail">
                                <span class="task-detail-label">Solution & Impact</span>
                                <span>${task.impact}</span>
                            </div>
                        `);
                    }
                }

                if (details.length > 0) {
                    taskDetailsHTML = `<div class="task-details">${details.join('')}</div>`;
                }

                taskItem.innerHTML = `
                    <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}
                           onchange="toggleTask(${index})">
                    <div class="task-content">
                        <div class="task-title">${task.task || 'Untitled Task'}</div>
                        ${taskDetailsHTML}
                    </div>
                    <div class="task-actions">
                        <button class="task-delete-btn" onclick="deleteTaskByIndex(${index})">
                            <i class="fas fa-trash"></i>
                            Delete
                        </button>
                    </div>
                `;
                taskList.appendChild(taskItem);
            });
        }

        async function addTask() {
            const input = document.getElementById('taskInput');
            const problemDesc = document.getElementById('problemDesc');
            const solutionDesc = document.getElementById('solutionDesc');
            const performanceImpact = document.getElementById('performanceImpact');

            const text = input.value.trim();
            const problem = problemDesc.value.trim();
            const solution = solutionDesc.value.trim();
            const impact = performanceImpact.value.trim();

            // Accept if any field has content
            if (text || problem || solution || impact) {
                // Only combine solution and impact if both have content
                let combinedImpact = '';
                if (solution && impact) {
                    combinedImpact = solution + ' | ' + impact;
                } else if (solution) {
                    combinedImpact = solution;
                } else if (impact) {
                    combinedImpact = impact;
                }

                const newTask = {
                    task: text,
                    problem: problem,
                    impact: combinedImpact,
                    completed: false
                };

                await saveTask(newTask);

                // Clear all inputs
                input.value = '';
                problemDesc.value = '';
                solutionDesc.value = '';
                performanceImpact.value = '';
            } else {
                alert('Please fill in at least one field');
            }
        }

        async function toggleTask(index) {
            const task = tasks[index];
            const newCompleted = !task.completed;
            await updateTask(task.id, {
                task: task.task,
                problem: task.problem,
                impact: task.impact,
                completed: newCompleted
            });
        }

        async function deleteTaskByIndex(index) {
            const task = tasks[index];
            await deleteTask(task.id);
        }

        async function clearCompleted() {
            const completedTasks = tasks.filter(task => task.completed);

            if (completedTasks.length === 0) {
                alert('No completed tasks to clear.');
                return;
            }

            if (confirm(`Are you sure you want to clear ${completedTasks.length} completed task(s)?`)) {
                // Delete all completed tasks from database
                for (const task of completedTasks) {
                    await deleteTask(task.id);
                }
            }
        }

        async function clearAll() {
            if (confirm('Are you sure you want to clear all tasks?')) {
                // Delete all tasks from database
                for (const task of tasks) {
                    await deleteTask(task.id);
                }
            }
        }

        // Event listeners
        document.getElementById('addBtn').addEventListener('click', addTask);
        document.getElementById('taskInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') addTask();
        });

        // Archival management functions
        async function archiveCompleted() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="spinner"></span> Archiving...';
            button.disabled = true;

            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const result = await lifecycleManager.performManualArchival('completed');

                if (result.archived > 0) {
                    alert(`Successfully archived ${result.archived} completed tasks`);
                    loadTasks(); // Refresh the task list
                } else {
                    alert('No completed tasks to archive');
                }
            } catch (error) {
                console.error('Error archiving tasks:', error);
                alert('Error archiving tasks. Please try again.');
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        async function viewArchivedTasks() {
            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const archivedData = await lifecycleManager.getArchivedTasks({
                    period: '30d',
                    includeStats: true,
                    limit: 50
                });

                // Open archived tasks in new window
                const archiveWindow = window.open('', '_blank', 'width=1200,height=800');
                archiveWindow.document.write(generateArchivedTasksHTML(archivedData));
            } catch (error) {
                console.error('Error viewing archived tasks:', error);
                alert('Error loading archived tasks. Please try again.');
            }
        }

        function generateArchivedTasksHTML(data) {
            const tasks = data.tasks || [];
            const stats = data.statistics || {};

            return `
                <html>
                <head>
                    <title>Archived Tasks</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
                        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }
                        .stat-card { background: white; padding: 15px; border: 1px solid #ddd; border-radius: 5px; text-align: center; }
                        .stat-value { font-size: 2em; font-weight: bold; color: #007bff; }
                        .stat-label { color: #666; font-size: 0.9em; }
                        .task-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; background: white; }
                        .task-header { font-weight: bold; color: #333; margin-bottom: 8px; }
                        .task-meta { font-size: 0.9em; color: #666; margin-bottom: 10px; }
                        .task-content { margin-bottom: 10px; }
                        .completed { background: #d4edda; border-color: #c3e6cb; }
                        .incomplete { background: #fff3cd; border-color: #ffeaa7; }
                        .search-box { width: 100%; padding: 10px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px; }
                    </style>
                    <script>
                        function filterTasks() {
                            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
                            const tasks = document.querySelectorAll('.task-item');

                            tasks.forEach(task => {
                                const text = task.textContent.toLowerCase();
                                task.style.display = text.includes(searchTerm) ? 'block' : 'none';
                            });
                        }
                    </script>
                </head>
                <body>
                    <div class="header">
                        <h1>Archived Tasks (Last 30 Days)</h1>
                        <p>Total archived tasks and their details from your daily work management system.</p>
                    </div>

                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-value">${stats.totalTasks || 0}</div>
                            <div class="stat-label">Total Archived</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.completedTasks || 0}</div>
                            <div class="stat-label">Completed Tasks</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.completionRate || 0}%</div>
                            <div class="stat-label">Completion Rate</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">${stats.qualityScore || 0}%</div>
                            <div class="stat-label">Quality Score</div>
                        </div>
                    </div>

                    <input type="text" id="searchBox" class="search-box" placeholder="Search archived tasks..." onkeyup="filterTasks()">

                    <h3>Archived Tasks</h3>
                    ${tasks.map(task => `
                        <div class="task-item ${task.completed ? 'completed' : 'incomplete'}">
                            <div class="task-header">${task.task || 'Untitled Task'}</div>
                            <div class="task-meta">
                                Archived: ${new Date(task.archivedAt).toLocaleDateString()} |
                                Status: ${task.completed ? 'Completed' : 'Incomplete'} |
                                Category: ${task.category || 'Other'} |
                                Reason: ${task.archiveReason || 'Daily archival'}
                            </div>
                            ${task.problem ? `<div class="task-content"><strong>Problem:</strong> ${task.problem}</div>` : ''}
                            ${task.impact ? `<div class="task-content"><strong>Solution:</strong> ${task.impact}</div>` : ''}
                        </div>
                    `).join('')}

                    ${tasks.length === 0 ? '<p style="text-align: center; color: #666; font-style: italic;">No archived tasks found.</p>' : ''}
                </body>
                </html>
            `;
        }

        function updateArchivalStatus() {
            const statusElement = document.getElementById('archivalStatus');
            if (!statusElement) return;

            try {
                const lifecycleManager = window.taskLifecycleManagerInstance || TaskLifecycleManager.getInstance();
                const status = lifecycleManager.getLifecycleStatus();

                const nextArchival = new Date(status.nextArchival);
                const lastArchival = status.lastArchival || 'Never';

                statusElement.innerHTML = `
                    Last archival: <strong>${lastArchival}</strong> |
                    Next archival: <strong>${nextArchival.toLocaleDateString()} at ${nextArchival.toLocaleTimeString()}</strong> |
                    Auto-archival: <strong>${status.autoArchival ? 'Enabled' : 'Disabled'}</strong>
                `;
            } catch (error) {
                statusElement.innerHTML = 'Error loading archival status';
            }
        }

        // Listen for archival events
        window.addEventListener('tasksArchived', () => {
            loadTasks();
            updateArchivalStatus();
        });

        // Initial render
        // Load tasks from database on page load
        loadTasks();

        // Update archival status after page loads
        setTimeout(updateArchivalStatus, 1000);
    </script>

    <!-- Design System JavaScript -->
    <script src="assets/js/design-system.js"></script>
    <script src="assets/js/task-lifecycle-manager.js"></script>
</body>
</html>




