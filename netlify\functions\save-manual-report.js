const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        const reportsCollection = db.collection('manual_reports');
        
        // Parse request body
        const reportData = JSON.parse(event.body);
        
        // Validate required fields
        if (!reportData.title || !reportData.date || !reportData.problems || !Array.isArray(reportData.problems) || reportData.problems.length === 0) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: 'Missing required fields',
                    required: ['title', 'date', 'problems (array with at least one problem)']
                })
            };
        }

        // Validate that at least one problem has required fields
        const validProblems = reportData.problems.filter(problem =>
            problem.name && problem.name.trim() &&
            problem.description && problem.description.trim() &&
            problem.solution && problem.solution.trim()
        );

        if (validProblems.length === 0) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    error: 'At least one problem must have name, description, and solution',
                    required: ['problem.name', 'problem.description', 'problem.solution']
                })
            };
        }

        // Process and clean problems data
        const processedProblems = reportData.problems.map((problem, index) => ({
            id: problem.id || `problem_${index + 1}`,
            number: index + 1,
            name: problem.name ? problem.name.trim() : '',
            description: problem.description ? problem.description.trim() : '',
            solution: problem.solution ? problem.solution.trim() : '',
            additional: problem.additional ? problem.additional.trim() : '',
            wordCount: {
                name: countWords(problem.name || ''),
                description: countWords(problem.description || ''),
                solution: countWords(problem.solution || ''),
                additional: countWords(problem.additional || '')
            }
        }));

        // Create report document
        const report = {
            title: reportData.title.trim(),
            date: reportData.date,
            problems: processedProblems,
            signatures: {
                employee: {
                    name: reportData.employeeSignature ? reportData.employeeSignature.trim() : '',
                    date: reportData.employeeDate || null
                },
                supervisor: {
                    name: reportData.supervisorSignature ? reportData.supervisorSignature.trim() : '',
                    date: reportData.supervisorDate || null
                }
            },
            metadata: {
                createdAt: new Date().toISOString(),
                type: 'manual_report',
                version: '2.0', // Updated version for multiple problems support
                problemCount: processedProblems.length,
                totalWordCount: calculateTotalWordCount(processedProblems),
                completeness: calculateCompleteness(reportData, processedProblems)
            },
            status: 'draft' // Can be 'draft', 'submitted', 'approved'
        };

        // Insert report into database
        const result = await reportsCollection.insertOne(report);
        
        // Return success response
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                success: true,
                message: 'Report saved successfully',
                report: {
                    id: result.insertedId.toString(),
                    title: report.title,
                    date: report.date,
                    createdAt: report.metadata.createdAt,
                    completeness: report.metadata.completeness
                }
            })
        };

    } catch (error) {
        console.error('Error saving manual report:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to save report',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

function countWords(text) {
    if (!text || typeof text !== 'string') return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
}

function calculateTotalWordCount(problems) {
    return problems.reduce((total, problem) => {
        return total +
            problem.wordCount.name +
            problem.wordCount.description +
            problem.wordCount.solution +
            problem.wordCount.additional;
    }, 0);
}

function calculateCompleteness(reportData, processedProblems) {
    let score = 0;
    const maxScore = 100;

    // Basic required fields (30% of score)
    if (reportData.title && reportData.title.trim()) score += 15;
    if (reportData.date) score += 15;

    // Problems completeness (50% of score)
    const problemsScore = processedProblems.reduce((problemScore, problem) => {
        let problemPoints = 0;

        // Required fields for each problem
        if (problem.name && problem.name.trim()) problemPoints += 5;
        if (problem.description && problem.description.trim()) problemPoints += 10;
        if (problem.solution && problem.solution.trim()) problemPoints += 10;

        // Optional but valuable
        if (problem.additional && problem.additional.trim()) problemPoints += 5;

        // Quality indicators
        if (problem.wordCount.description >= 20) problemPoints += 5; // Detailed description
        if (problem.wordCount.solution >= 20) problemPoints += 5; // Detailed solution

        return problemScore + problemPoints;
    }, 0);

    // Average problem score and scale to 50% of total
    const avgProblemScore = processedProblems.length > 0 ? problemsScore / processedProblems.length : 0;
    score += Math.min(avgProblemScore * (50 / 40), 50); // Scale to max 50 points

    // Signatures (20% of score)
    if (reportData.employeeSignature && reportData.employeeSignature.trim()) score += 10;
    if (reportData.supervisorSignature && reportData.supervisorSignature.trim()) score += 10;

    return Math.round(Math.min(score, maxScore));
}
