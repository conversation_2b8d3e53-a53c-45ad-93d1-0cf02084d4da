/**
 * Task Lifecycle Manager
 * Handles automated daily archival, task carryover, and lifecycle management
 */

class TaskLifecycleManager {
    constructor() {
        this.API_BASE = '/.netlify/functions';
        this.STORAGE_KEY = 'taskLifecycleManager';
        this.init();
    }

    init() {
        this.loadSettings();
        this.setupScheduler();
        this.setupEventListeners();
        this.checkDailyArchival();
    }

    loadSettings() {
        const saved = localStorage.getItem(this.STORAGE_KEY);
        this.settings = saved ? JSON.parse(saved) : {
            autoArchival: true,
            archivalTime: '23:55', // 5 minutes before daily snapshot
            archivalStrategy: 'completed', // completed, all, selective
            carryoverIncomplete: true,
            maxCarryoverDays: 3,
            lastArchival: null,
            notifications: true,
            enabled: true
        };
    }

    saveSettings() {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.settings));
    }

    async checkDailyArchival() {
        const today = new Date().toISOString().split('T')[0];
        
        // Check if we already archived today
        if (this.settings.lastArchival === today) {
            console.log('Daily archival already completed for today');
            return;
        }

        // Check if it's time for automatic archival
        if (this.settings.autoArchival && this.shouldPerformArchival()) {
            await this.performDailyArchival();
        }
    }

    shouldPerformArchival() {
        const now = new Date();
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
        
        return currentTime >= this.settings.archivalTime;
    }

    async performDailyArchival(isManual = false) {
        try {
            console.log('Starting daily task archival...');
            
            // First, preview what will be archived
            const preview = await this.previewArchival();
            
            if (preview.tasksToArchive === 0) {
                console.log('No tasks to archive');
                if (isManual) {
                    this.showNotification('No tasks to archive', 'info');
                }
                return { success: true, archived: 0 };
            }

            // Perform the archival
            const result = await fetch(`${this.API_BASE}/archive-daily-tasks`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    mode: isManual ? 'manual' : 'auto',
                    strategy: this.settings.archivalStrategy,
                    date: new Date().toISOString().split('T')[0]
                })
            });

            if (result.ok) {
                const archivalResult = await result.json();
                console.log('Daily archival completed:', archivalResult);
                
                // Update settings
                this.settings.lastArchival = new Date().toISOString().split('T')[0];
                this.saveSettings();
                
                // Show notification
                if (this.settings.notifications || isManual) {
                    this.showNotification(
                        `Archived ${archivalResult.archived} tasks successfully`,
                        'success'
                    );
                }
                
                // Refresh task list if user is on todo page
                if (window.location.pathname.includes('todo-list.html')) {
                    this.refreshTaskList();
                }
                
                return archivalResult;
            } else {
                throw new Error(`HTTP ${result.status}: ${result.statusText}`);
            }
        } catch (error) {
            console.error('Error performing daily archival:', error);
            this.showNotification('Error during daily archival', 'error');
            throw error;
        }
    }

    async previewArchival(date = null, strategy = null) {
        try {
            const archiveDate = date || new Date().toISOString().split('T')[0];
            const archiveStrategy = strategy || this.settings.archivalStrategy;
            
            const response = await fetch(
                `${this.API_BASE}/archive-daily-tasks?mode=preview&date=${archiveDate}&strategy=${archiveStrategy}`
            );
            
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error previewing archival:', error);
            throw error;
        }
    }

    async getArchivedTasks(options = {}) {
        try {
            const params = new URLSearchParams();
            
            // Add parameters
            if (options.dateFrom) params.append('dateFrom', options.dateFrom);
            if (options.dateTo) params.append('dateTo', options.dateTo);
            if (options.period) params.append('period', options.period);
            if (options.category) params.append('category', options.category);
            if (options.completed !== undefined) params.append('completed', options.completed);
            if (options.limit) params.append('limit', options.limit);
            if (options.page) params.append('page', options.page);
            if (options.sortBy) params.append('sortBy', options.sortBy);
            if (options.sortOrder) params.append('sortOrder', options.sortOrder);
            if (options.includeStats) params.append('includeStats', 'true');
            
            const response = await fetch(`${this.API_BASE}/get-archived-tasks?${params}`);
            
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error fetching archived tasks:', error);
            throw error;
        }
    }

    setupScheduler() {
        // Check every hour if we need to perform archival
        setInterval(() => {
            this.checkDailyArchival();
        }, 60 * 60 * 1000); // 1 hour

        // More frequent checks during the archival hour
        const now = new Date();
        const archivalHour = parseInt(this.settings.archivalTime.split(':')[0]);
        
        if (now.getHours() === archivalHour) {
            setInterval(() => {
                this.checkDailyArchival();
            }, 5 * 60 * 1000); // 5 minutes
        }
    }

    setupEventListeners() {
        // Listen for page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkDailyArchival();
            }
        });

        // Listen for beforeunload to perform archival if needed
        window.addEventListener('beforeunload', () => {
            if (this.shouldPerformArchival() && 
                this.settings.lastArchival !== new Date().toISOString().split('T')[0]) {
                // Use sendBeacon for reliable delivery
                navigator.sendBeacon(
                    `${this.API_BASE}/archive-daily-tasks`,
                    JSON.stringify({
                        mode: 'auto',
                        strategy: this.settings.archivalStrategy
                    })
                );
            }
        });
    }

    refreshTaskList() {
        // Refresh the task list if the loadTasks function exists
        if (typeof loadTasks === 'function') {
            loadTasks();
        }
        
        // Dispatch custom event for other components to listen
        window.dispatchEvent(new CustomEvent('tasksArchived', {
            detail: { timestamp: new Date().toISOString() }
        }));
    }

    showNotification(message, type = 'info') {
        // Use design system notification if available
        if (window.designSystemInstance) {
            window.designSystemInstance.showAlert(message, type, {
                autoDismiss: true,
                dismissible: true
            });
        } else {
            console.log(`Task Lifecycle: ${message}`);
        }
    }

    // Configuration methods
    setAutoArchival(enabled) {
        this.settings.autoArchival = enabled;
        this.saveSettings();
    }

    setArchivalTime(time) {
        this.settings.archivalTime = time;
        this.saveSettings();
    }

    setArchivalStrategy(strategy) {
        this.settings.archivalStrategy = strategy;
        this.saveSettings();
    }

    setCarryoverSettings(enabled, maxDays = 3) {
        this.settings.carryoverIncomplete = enabled;
        this.settings.maxCarryoverDays = maxDays;
        this.saveSettings();
    }

    // Manual operations
    async performManualArchival(strategy = null) {
        const archiveStrategy = strategy || this.settings.archivalStrategy;
        this.settings.archivalStrategy = archiveStrategy;
        this.saveSettings();
        
        return await this.performDailyArchival(true);
    }

    async restoreTask(taskId) {
        try {
            // This would need a corresponding Netlify function
            const response = await fetch(`${this.API_BASE}/restore-task`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ taskId })
            });

            if (response.ok) {
                const result = await response.json();
                this.showNotification('Task restored successfully', 'success');
                this.refreshTaskList();
                return result;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error restoring task:', error);
            this.showNotification('Error restoring task', 'error');
            throw error;
        }
    }

    // Status and reporting
    getLifecycleStatus() {
        const nextArchival = this.getNextArchivalTime();
        const lastArchival = this.settings.lastArchival || 'Never';
        
        return {
            ...this.settings,
            nextArchival,
            lastArchival,
            canArchiveNow: this.settings.lastArchival !== new Date().toISOString().split('T')[0]
        };
    }

    getNextArchivalTime() {
        const now = new Date();
        const today = new Date().toISOString().split('T')[0];
        
        if (this.settings.lastArchival === today) {
            // Next archival is tomorrow
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow.toISOString().split('T')[0] + 'T' + this.settings.archivalTime + ':00';
        } else {
            // Next archival is today (if time hasn't passed) or tomorrow
            const [hours, minutes] = this.settings.archivalTime.split(':');
            const archivalTime = new Date(now);
            archivalTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            
            if (archivalTime <= now) {
                // Time has passed, next is tomorrow
                archivalTime.setDate(archivalTime.getDate() + 1);
            }
            
            return archivalTime.toISOString();
        }
    }

    // Analytics integration
    async getArchivalAnalytics(period = '30d') {
        try {
            const archivedTasks = await this.getArchivedTasks({
                period,
                includeStats: true
            });
            
            return {
                totalArchived: archivedTasks.statistics?.totalTasks || 0,
                completionRate: archivedTasks.statistics?.completionRate || 0,
                qualityScore: archivedTasks.statistics?.qualityScore || 0,
                dailyBreakdown: this.calculateDailyBreakdown(archivedTasks.tasks),
                categoryBreakdown: this.calculateCategoryBreakdown(archivedTasks.tasks)
            };
        } catch (error) {
            console.error('Error getting archival analytics:', error);
            return null;
        }
    }

    calculateDailyBreakdown(tasks) {
        const breakdown = {};
        tasks.forEach(task => {
            const date = task.archiveDate;
            if (!breakdown[date]) {
                breakdown[date] = { total: 0, completed: 0 };
            }
            breakdown[date].total++;
            if (task.completed) {
                breakdown[date].completed++;
            }
        });
        return breakdown;
    }

    calculateCategoryBreakdown(tasks) {
        const breakdown = {
            'Technical Issues': 0,
            'Maintenance': 0,
            'User Support': 0,
            'System Updates': 0,
            'Other': 0
        };
        
        tasks.forEach(task => {
            const category = task.category || 'Other';
            if (breakdown.hasOwnProperty(category)) {
                breakdown[category]++;
            } else {
                breakdown['Other']++;
            }
        });
        
        return breakdown;
    }

    // Public API
    static getInstance() {
        if (!window.taskLifecycleManagerInstance) {
            window.taskLifecycleManagerInstance = new TaskLifecycleManager();
        }
        return window.taskLifecycleManagerInstance;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        TaskLifecycleManager.getInstance();
    });
} else {
    TaskLifecycleManager.getInstance();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TaskLifecycleManager;
}
