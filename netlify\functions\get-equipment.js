const { connectToDatabase } = require('./utils/mongodb');

exports.handler = async (event, context) => {
  // Handle CORS preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: ''
    };
  }

  try {
    const { db } = await connectToDatabase();
    
    const equipment = await db.collection('equipment').find({}).toArray();
    
    // Convert MongoDB _id to id
    const formattedEquipment = equipment.map(item => ({
      id: item._id.toString(),
      type: item.type,
      name: item.name,
      quantity: item.quantity,
      priority: item.priority,
      forUser: item.forUser,
      reason: item.reason,
      specifications: item.specifications,
      status: item.status,
      createdAt: item.createdAt
    }));

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: JSON.stringify(formattedEquipment)
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS'
      },
      body: JSON.stringify({ error: error.message })
    };
  }
};
