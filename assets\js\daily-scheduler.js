/**
 * Daily Scheduler for Automated Task Snapshots
 * Handles automatic daily archival and scheduling
 */

class DailyScheduler {
    constructor() {
        this.API_BASE = '/.netlify/functions';
        this.STORAGE_KEY = 'dailyScheduler';
        this.init();
    }

    init() {
        this.loadSchedulerState();
        this.checkDailySnapshot();
        this.setupScheduler();
        this.setupEventListeners();
    }

    loadSchedulerState() {
        const saved = localStorage.getItem(this.STORAGE_KEY);
        this.state = saved ? JSON.parse(saved) : {
            lastSnapshot: null,
            autoSnapshot: true,
            snapshotTime: '23:59', // Default to end of day
            enabled: true
        };
    }

    saveSchedulerState() {
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.state));
    }

    async checkDailySnapshot() {
        const today = new Date().toISOString().split('T')[0];
        
        // Check if we already created a snapshot today
        if (this.state.lastSnapshot === today) {
            console.log('Daily snapshot already created for today');
            return;
        }

        // Check if it's time for automatic snapshot
        if (this.state.autoSnapshot && this.shouldCreateSnapshot()) {
            await this.createDailySnapshot();
        }
    }

    shouldCreateSnapshot() {
        const now = new Date();
        const currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                           now.getMinutes().toString().padStart(2, '0');
        
        // Check if current time is past the scheduled snapshot time
        return currentTime >= this.state.snapshotTime;
    }

    async createDailySnapshot() {
        try {
            console.log('Creating daily snapshot...');
            
            const response = await fetch(`${this.API_BASE}/daily-snapshot`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('Daily snapshot created successfully:', result);
                
                // Update state
                this.state.lastSnapshot = new Date().toISOString().split('T')[0];
                this.saveSchedulerState();
                
                // Notify user if they're on the dashboard
                if (window.location.pathname.includes('dashboard.html')) {
                    this.showSnapshotNotification('Daily snapshot created successfully');
                }
                
                return result;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error creating daily snapshot:', error);
            this.showSnapshotNotification('Error creating daily snapshot', 'error');
            throw error;
        }
    }

    setupScheduler() {
        // Check every hour if we need to create a snapshot
        setInterval(() => {
            this.checkDailySnapshot();
        }, 60 * 60 * 1000); // 1 hour

        // Also check every 10 minutes during the last hour of the day
        const now = new Date();
        if (now.getHours() === 23) {
            setInterval(() => {
                this.checkDailySnapshot();
            }, 10 * 60 * 1000); // 10 minutes
        }
    }

    setupEventListeners() {
        // Listen for page visibility changes to check snapshots when user returns
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.checkDailySnapshot();
            }
        });

        // Listen for beforeunload to create snapshot if needed
        window.addEventListener('beforeunload', () => {
            if (this.shouldCreateSnapshot() && this.state.lastSnapshot !== new Date().toISOString().split('T')[0]) {
                // Use sendBeacon for reliable delivery
                navigator.sendBeacon(`${this.API_BASE}/daily-snapshot`, JSON.stringify({}));
            }
        });
    }

    showSnapshotNotification(message, type = 'success') {
        // Use design system notification if available
        if (window.designSystemInstance) {
            window.designSystemInstance.showAlert(message, type, {
                autoDismiss: true,
                dismissible: true
            });
        } else {
            console.log(`Snapshot notification: ${message}`);
        }
    }

    // Manual snapshot creation
    async createManualSnapshot() {
        try {
            const result = await this.createDailySnapshot();
            this.showSnapshotNotification('Manual snapshot created successfully');
            return result;
        } catch (error) {
            this.showSnapshotNotification('Error creating manual snapshot', 'error');
            throw error;
        }
    }

    // Configuration methods
    setAutoSnapshot(enabled) {
        this.state.autoSnapshot = enabled;
        this.saveSchedulerState();
    }

    setSnapshotTime(time) {
        this.state.snapshotTime = time;
        this.saveSchedulerState();
    }

    getSchedulerStatus() {
        return {
            ...this.state,
            nextSnapshot: this.getNextSnapshotTime(),
            canCreateManual: this.state.lastSnapshot !== new Date().toISOString().split('T')[0]
        };
    }

    getNextSnapshotTime() {
        const now = new Date();
        const today = new Date().toISOString().split('T')[0];
        
        if (this.state.lastSnapshot === today) {
            // Next snapshot is tomorrow
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow.toISOString().split('T')[0] + 'T' + this.state.snapshotTime + ':00';
        } else {
            // Next snapshot is today (if time hasn't passed) or tomorrow
            const [hours, minutes] = this.state.snapshotTime.split(':');
            const snapshotTime = new Date(now);
            snapshotTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            
            if (snapshotTime <= now) {
                // Time has passed, next is tomorrow
                snapshotTime.setDate(snapshotTime.getDate() + 1);
            }
            
            return snapshotTime.toISOString();
        }
    }

    // Historical data utilities
    async getHistoricalData(period = '30d') {
        try {
            const response = await fetch(`${this.API_BASE}/get-daily-snapshots?period=${period}`);
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error fetching historical data:', error);
            throw error;
        }
    }

    async getSpecificMetrics(metrics, dateFrom, dateTo) {
        try {
            let url = `${this.API_BASE}/get-daily-snapshots?metrics=${metrics.join(',')}`;
            if (dateFrom && dateTo) {
                url += `&dateFrom=${dateFrom}&dateTo=${dateTo}`;
            }
            
            const response = await fetch(url);
            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error('Error fetching specific metrics:', error);
            throw error;
        }
    }

    // Backfill utility for existing data
    async backfillHistoricalData(days = 30) {
        try {
            console.log(`Starting backfill for ${days} days...`);
            const results = [];
            
            for (let i = days; i >= 1; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateString = date.toISOString().split('T')[0];
                
                // Check if snapshot already exists
                const existing = await this.checkSnapshotExists(dateString);
                if (existing) {
                    console.log(`Snapshot already exists for ${dateString}`);
                    continue;
                }
                
                // Create backfill snapshot
                const result = await this.createBackfillSnapshot(dateString);
                results.push(result);
                
                // Add delay to avoid overwhelming the database
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            console.log(`Backfill completed. Created ${results.length} snapshots.`);
            return results;
        } catch (error) {
            console.error('Error during backfill:', error);
            throw error;
        }
    }

    async checkSnapshotExists(date) {
        try {
            const response = await fetch(`${this.API_BASE}/get-daily-snapshots?dateFrom=${date}&dateTo=${date}`);
            if (response.ok) {
                const data = await response.json();
                return data.snapshots && data.snapshots.length > 0;
            }
            return false;
        } catch (error) {
            console.error('Error checking snapshot existence:', error);
            return false;
        }
    }

    async createBackfillSnapshot(date) {
        try {
            const response = await fetch(`${this.API_BASE}/daily-snapshot`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ backfillDate: date })
            });

            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        } catch (error) {
            console.error(`Error creating backfill snapshot for ${date}:`, error);
            throw error;
        }
    }

    // Public API for dashboard integration
    static getInstance() {
        if (!window.dailySchedulerInstance) {
            window.dailySchedulerInstance = new DailyScheduler();
        }
        return window.dailySchedulerInstance;
    }
}

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        DailyScheduler.getInstance();
    });
} else {
    DailyScheduler.getInstance();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DailyScheduler;
}
