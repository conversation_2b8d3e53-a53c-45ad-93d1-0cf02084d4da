/**
 * Design System JavaScript
 * Handles theme switching, component interactions, and utilities
 */

class DesignSystem {
  constructor() {
    this.init();
  }

  init() {
    this.initTheme();
    this.initTabs();
    this.initAlerts();
    this.initLoadingStates();
    this.initFormValidation();
  }

  // ===== THEME MANAGEMENT =====
  initTheme() {
    // Get saved theme or default to light
    const savedTheme = localStorage.getItem('theme') || 'light';
    this.setTheme(savedTheme);

    // Create theme toggle button
    this.createThemeToggle();
  }

  createThemeToggle() {
    const toggle = document.createElement('button');
    toggle.className = 'theme-toggle';
    toggle.setAttribute('aria-label', 'Toggle theme');
    toggle.innerHTML = '<i class="fas fa-moon"></i>';
    
    toggle.addEventListener('click', () => {
      const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
      const newTheme = currentTheme === 'light' ? 'dark' : 'light';
      this.setTheme(newTheme);
    });

    document.body.appendChild(toggle);
  }

  setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    
    // Update toggle icon
    const toggle = document.querySelector('.theme-toggle i');
    if (toggle) {
      toggle.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    }
  }

  // ===== TAB MANAGEMENT =====
  initTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    
    tabButtons.forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        this.switchTab(button);
      });
    });
  }

  switchTab(activeButton) {
    const tabContainer = activeButton.closest('.tabs').parentElement;
    const targetId = activeButton.getAttribute('data-tab');
    
    // Remove active class from all buttons and contents
    tabContainer.querySelectorAll('.tab-button').forEach(btn => {
      btn.classList.remove('active');
    });
    tabContainer.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    
    // Add active class to clicked button and corresponding content
    activeButton.classList.add('active');
    const targetContent = tabContainer.querySelector(`#${targetId}`);
    if (targetContent) {
      targetContent.classList.add('active');
    }
  }

  // ===== ALERT MANAGEMENT =====
  initAlerts() {
    // Auto-dismiss alerts after 5 seconds
    const alerts = document.querySelectorAll('.alert[data-auto-dismiss="true"]');
    alerts.forEach(alert => {
      setTimeout(() => {
        this.dismissAlert(alert);
      }, 5000);
    });

    // Add close buttons to dismissible alerts
    const dismissibleAlerts = document.querySelectorAll('.alert[data-dismissible="true"]');
    dismissibleAlerts.forEach(alert => {
      const closeBtn = document.createElement('button');
      closeBtn.innerHTML = '<i class="fas fa-times"></i>';
      closeBtn.className = 'alert-close';
      closeBtn.style.cssText = `
        background: none;
        border: none;
        cursor: pointer;
        margin-left: auto;
        padding: 0;
        color: inherit;
        opacity: 0.7;
      `;
      closeBtn.addEventListener('click', () => this.dismissAlert(alert));
      alert.appendChild(closeBtn);
    });
  }

  dismissAlert(alert) {
    alert.style.opacity = '0';
    alert.style.transform = 'translateX(100%)';
    setTimeout(() => {
      alert.remove();
    }, 300);
  }

  showAlert(message, type = 'success', options = {}) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    
    const icon = this.getAlertIcon(type);
    alert.innerHTML = `
      <i class="alert-icon ${icon}"></i>
      <div>${message}</div>
    `;

    if (options.dismissible !== false) {
      alert.setAttribute('data-dismissible', 'true');
    }

    if (options.autoDismiss !== false) {
      alert.setAttribute('data-auto-dismiss', 'true');
    }

    // Insert at top of body or specified container
    const container = options.container || document.body;
    container.insertBefore(alert, container.firstChild);

    // Initialize alert functionality
    this.initAlerts();

    return alert;
  }

  getAlertIcon(type) {
    const icons = {
      success: 'fas fa-check-circle',
      warning: 'fas fa-exclamation-triangle',
      error: 'fas fa-exclamation-circle',
      info: 'fas fa-info-circle'
    };
    return icons[type] || icons.info;
  }

  // ===== LOADING STATES =====
  initLoadingStates() {
    // Add loading state to buttons on form submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
      form.addEventListener('submit', (e) => {
        const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitBtn) {
          this.setLoadingState(submitBtn, true);
        }
      });
    });
  }

  setLoadingState(element, isLoading) {
    if (isLoading) {
      element.disabled = true;
      element.setAttribute('data-original-text', element.textContent);
      element.innerHTML = '<span class="spinner"></span> Loading...';
      element.classList.add('loading');
    } else {
      element.disabled = false;
      element.textContent = element.getAttribute('data-original-text') || 'Submit';
      element.classList.remove('loading');
      element.removeAttribute('data-original-text');
    }
  }

  // ===== FORM VALIDATION =====
  initFormValidation() {
    const inputs = document.querySelectorAll('.form-input, .form-select, .form-textarea');
    
    inputs.forEach(input => {
      input.addEventListener('blur', () => this.validateField(input));
      input.addEventListener('input', () => this.clearFieldError(input));
    });
  }

  validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    const type = field.getAttribute('type');
    
    let isValid = true;
    let errorMessage = '';

    // Required validation
    if (isRequired && !value) {
      isValid = false;
      errorMessage = 'This field is required';
    }

    // Email validation
    if (type === 'email' && value && !this.isValidEmail(value)) {
      isValid = false;
      errorMessage = 'Please enter a valid email address';
    }

    // URL validation
    if (type === 'url' && value && !this.isValidUrl(value)) {
      isValid = false;
      errorMessage = 'Please enter a valid URL';
    }

    // Custom validation
    const customValidator = field.getAttribute('data-validator');
    if (customValidator && value) {
      const validatorFn = this[customValidator];
      if (validatorFn && !validatorFn(value)) {
        isValid = false;
        errorMessage = field.getAttribute('data-error-message') || 'Invalid input';
      }
    }

    this.setFieldValidation(field, isValid, errorMessage);
    return isValid;
  }

  setFieldValidation(field, isValid, errorMessage = '') {
    const formGroup = field.closest('.form-group');
    if (!formGroup) return;

    // Remove existing error
    const existingError = formGroup.querySelector('.field-error');
    if (existingError) {
      existingError.remove();
    }

    // Update field styling
    field.classList.remove('field-valid', 'field-invalid');
    
    if (isValid) {
      field.classList.add('field-valid');
    } else {
      field.classList.add('field-invalid');
      
      // Add error message
      const errorEl = document.createElement('div');
      errorEl.className = 'field-error';
      errorEl.textContent = errorMessage;
      errorEl.style.cssText = `
        color: var(--error-600);
        font-size: var(--font-size-sm);
        margin-top: var(--space-1);
      `;
      formGroup.appendChild(errorEl);
    }
  }

  clearFieldError(field) {
    field.classList.remove('field-invalid');
    const formGroup = field.closest('.form-group');
    if (formGroup) {
      const error = formGroup.querySelector('.field-error');
      if (error) {
        error.remove();
      }
    }
  }

  // ===== UTILITY FUNCTIONS =====
  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // ===== PUBLIC API =====
  static getInstance() {
    if (!window.designSystemInstance) {
      window.designSystemInstance = new DesignSystem();
    }
    return window.designSystemInstance;
  }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    DesignSystem.getInstance();
  });
} else {
  DesignSystem.getInstance();
}

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = DesignSystem;
}
