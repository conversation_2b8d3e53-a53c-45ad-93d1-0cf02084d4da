const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        const reportsCollection = db.collection('manual_reports');
        
        // Parse query parameters
        const params = new URLSearchParams(event.queryString || '');
        const reportId = params.get('id');
        const dateFrom = params.get('dateFrom');
        const dateTo = params.get('dateTo');
        const status = params.get('status');
        const limit = parseInt(params.get('limit')) || 50;
        const page = parseInt(params.get('page')) || 1;
        const sortBy = params.get('sortBy') || 'metadata.createdAt';
        const sortOrder = params.get('sortOrder') || 'desc';
        const includeStats = params.get('includeStats') === 'true';

        // If specific report ID requested
        if (reportId) {
            const report = await reportsCollection.findOne({ 
                _id: new require('mongodb').ObjectId(reportId) 
            });
            
            if (!report) {
                return {
                    statusCode: 404,
                    headers,
                    body: JSON.stringify({ error: 'Report not found' })
                };
            }

            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    report: formatReportForResponse(report)
                })
            };
        }

        // Build query for multiple reports
        let query = {};
        
        // Date filtering
        if (dateFrom && dateTo) {
            query.date = {
                $gte: dateFrom,
                $lte: dateTo
            };
        } else if (dateFrom) {
            query.date = { $gte: dateFrom };
        } else if (dateTo) {
            query.date = { $lte: dateTo };
        }

        // Status filtering
        if (status && status !== 'all') {
            query.status = status;
        }

        // Calculate pagination
        const skip = (page - 1) * limit;
        
        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute queries
        const [reports, totalCount] = await Promise.all([
            reportsCollection
                .find(query)
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .toArray(),
            reportsCollection.countDocuments(query)
        ]);

        // Format reports for response
        const formattedReports = reports.map(formatReportForResponse);

        let result = {
            reports: formattedReports,
            pagination: {
                page: page,
                limit: limit,
                total: totalCount,
                pages: Math.ceil(totalCount / limit),
                hasNext: page < Math.ceil(totalCount / limit),
                hasPrev: page > 1
            },
            query: {
                dateFrom,
                dateTo,
                status,
                sortBy,
                sortOrder
            }
        };

        // Include statistics if requested
        if (includeStats) {
            result.statistics = await generateReportStatistics(reportsCollection, query);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result)
        };

    } catch (error) {
        console.error('Error fetching manual reports:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to fetch reports',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

function formatReportForResponse(report) {
    return {
        id: report._id.toString(),
        title: report.title,
        date: report.date,
        problems: report.problems || [], // Handle both old and new format
        // Legacy fields for backward compatibility
        problem: report.problem || (report.problems && report.problems[0] ? report.problems[0].description : ''),
        solution: report.solution || (report.problems && report.problems[0] ? report.problems[0].solution : ''),
        additional: report.additional || (report.problems && report.problems[0] ? report.problems[0].additional : ''),
        signatures: report.signatures,
        status: report.status,
        createdAt: report.metadata.createdAt,
        wordCount: report.metadata.wordCount || report.metadata.totalWordCount,
        completeness: report.metadata.completeness,
        version: report.metadata.version,
        problemCount: report.metadata.problemCount || (report.problems ? report.problems.length : 1)
    };
}

async function generateReportStatistics(collection, query) {
    const pipeline = [
        { $match: query },
        {
            $group: {
                _id: null,
                totalReports: { $sum: 1 },
                avgCompleteness: { $avg: '$metadata.completeness' },
                avgProblemWords: { $avg: '$metadata.wordCount.problem' },
                avgSolutionWords: { $avg: '$metadata.wordCount.solution' },
                reportsWithSignatures: {
                    $sum: {
                        $cond: [
                            {
                                $and: [
                                    { $ne: ['$signatures.employee.name', ''] },
                                    { $ne: ['$signatures.supervisor.name', ''] }
                                ]
                            },
                            1,
                            0
                        ]
                    }
                },
                statusBreakdown: {
                    $push: '$status'
                }
            }
        }
    ];

    const [stats] = await collection.aggregate(pipeline).toArray();
    
    if (!stats) {
        return {
            totalReports: 0,
            avgCompleteness: 0,
            avgProblemWords: 0,
            avgSolutionWords: 0,
            reportsWithSignatures: 0,
            signatureRate: 0,
            statusBreakdown: {}
        };
    }

    // Calculate status breakdown
    const statusBreakdown = {};
    stats.statusBreakdown.forEach(status => {
        statusBreakdown[status] = (statusBreakdown[status] || 0) + 1;
    });

    return {
        totalReports: stats.totalReports,
        avgCompleteness: Math.round(stats.avgCompleteness || 0),
        avgProblemWords: Math.round(stats.avgProblemWords || 0),
        avgSolutionWords: Math.round(stats.avgSolutionWords || 0),
        reportsWithSignatures: stats.reportsWithSignatures,
        signatureRate: Math.round((stats.reportsWithSignatures / stats.totalReports) * 100),
        statusBreakdown: statusBreakdown
    };
}

// Additional utility functions for report management
async function updateReportStatus(db, reportId, newStatus) {
    const reportsCollection = db.collection('manual_reports');
    
    const result = await reportsCollection.updateOne(
        { _id: new require('mongodb').ObjectId(reportId) },
        { 
            $set: { 
                status: newStatus,
                'metadata.lastModified': new Date().toISOString()
            }
        }
    );
    
    return result.modifiedCount > 0;
}

async function deleteReport(db, reportId) {
    const reportsCollection = db.collection('manual_reports');
    
    const result = await reportsCollection.deleteOne({
        _id: new require('mongodb').ObjectId(reportId)
    });
    
    return result.deletedCount > 0;
}

async function searchReports(db, searchTerm, limit = 20) {
    const reportsCollection = db.collection('manual_reports');

    const searchQuery = {
        $or: [
            { title: { $regex: searchTerm, $options: 'i' } },
            // Legacy fields
            { problem: { $regex: searchTerm, $options: 'i' } },
            { solution: { $regex: searchTerm, $options: 'i' } },
            { additional: { $regex: searchTerm, $options: 'i' } },
            // New problems array fields
            { 'problems.name': { $regex: searchTerm, $options: 'i' } },
            { 'problems.description': { $regex: searchTerm, $options: 'i' } },
            { 'problems.solution': { $regex: searchTerm, $options: 'i' } },
            { 'problems.additional': { $regex: searchTerm, $options: 'i' } }
        ]
    };

    const reports = await reportsCollection
        .find(searchQuery)
        .sort({ 'metadata.createdAt': -1 })
        .limit(limit)
        .toArray();

    return reports.map(formatReportForResponse);
}
