/* ===== DESIGN SYSTEM FOUNDATION ===== */

/* CSS Custom Properties (Design Tokens) */
:root {
  /* Color Palette */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --secondary-50: #f8fafc;
  --secondary-100: #f1f5f9;
  --secondary-200: #e2e8f0;
  --secondary-300: #cbd5e1;
  --secondary-400: #94a3b8;
  --secondary-500: #64748b;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1e293b;
  --secondary-900: #0f172a;

  --success-50: #f0fdf4;
  --success-100: #dcfce7;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;

  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* Spacing */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */

  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

/* Dark Mode Variables */
[data-theme="dark"] {
  --primary-50: #1e3a8a;
  --primary-100: #1e40af;
  --primary-200: #1d4ed8;
  --primary-300: #2563eb;
  --primary-400: #3b82f6;
  --primary-500: #60a5fa;
  --primary-600: #93c5fd;
  --primary-700: #bfdbfe;
  --primary-800: #dbeafe;
  --primary-900: #eff6ff;

  --secondary-50: #0f172a;
  --secondary-100: #1e293b;
  --secondary-200: #334155;
  --secondary-300: #475569;
  --secondary-400: #64748b;
  --secondary-500: #94a3b8;
  --secondary-600: #cbd5e1;
  --secondary-700: #e2e8f0;
  --secondary-800: #f1f5f9;
  --secondary-900: #f8fafc;
}

/* ===== BASE STYLES ===== */

* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--secondary-800);
  background-color: var(--secondary-50);
  margin: 0;
  padding: 0;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* ===== TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin: 0 0 var(--space-4) 0;
  color: var(--secondary-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin: 0 0 var(--space-4) 0;
  color: var(--secondary-700);
}

/* ===== COMPONENT STYLES ===== */

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: var(--secondary-100);
  color: var(--secondary-700);
  border-color: var(--secondary-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--secondary-200);
  border-color: var(--secondary-400);
}

.btn-success {
  background-color: var(--success-600);
  color: white;
  border-color: var(--success-600);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--success-700);
  border-color: var(--success-700);
}

.btn-warning {
  background-color: var(--warning-500);
  color: white;
  border-color: var(--warning-500);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--warning-600);
  border-color: var(--warning-600);
}

.btn-error {
  background-color: var(--error-500);
  color: white;
  border-color: var(--error-500);
}

.btn-error:hover:not(:disabled) {
  background-color: var(--error-600);
  border-color: var(--error-600);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--font-size-lg);
}

/* Cards */
.card {
  background: white;
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--secondary-200);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--secondary-200);
  background-color: var(--secondary-50);
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
}

/* Forms */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--secondary-700);
  margin-bottom: var(--space-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--font-size-base);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-lg);
  background-color: white;
  transition: all var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px rgb(59 130 246 / 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

/* Utilities */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: var(--font-weight-bold); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-medium { font-weight: var(--font-weight-medium); }

.text-primary { color: var(--primary-600); }
.text-secondary { color: var(--secondary-600); }
.text-success { color: var(--success-600); }
.text-warning { color: var(--warning-600); }
.text-error { color: var(--error-600); }

.bg-primary { background-color: var(--primary-600); }
.bg-secondary { background-color: var(--secondary-100); }
.bg-success { background-color: var(--success-50); }
.bg-warning { background-color: var(--warning-50); }
.bg-error { background-color: var(--error-50); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* Spacing utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: var(--space-2); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }

/* Grid utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

/* ===== THEME TOGGLE ===== */
.theme-toggle {
  position: fixed;
  top: var(--space-4);
  right: var(--space-4);
  z-index: var(--z-fixed);
  background: var(--secondary-100);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-full);
  padding: var(--space-3);
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-md);
}

.theme-toggle:hover {
  background: var(--secondary-200);
  transform: scale(1.05);
}

.theme-toggle i {
  font-size: var(--font-size-lg);
  color: var(--secondary-700);
}

/* ===== NAVIGATION ===== */
.nav-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-6);
  margin: var(--space-8) 0;
}

.nav-card {
  background: white;
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  text-decoration: none;
  color: inherit;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
  transform: scaleX(0);
  transition: transform var(--transition-normal);
}

.nav-card:hover::before {
  transform: scaleX(1);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-200);
}

.nav-card-icon {
  font-size: 3rem;
  margin-bottom: var(--space-4);
  display: block;
  text-align: center;
}

.nav-card-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--secondary-900);
  margin-bottom: var(--space-3);
  text-align: center;
}

.nav-card-description {
  color: var(--secondary-600);
  line-height: var(--line-height-relaxed);
  text-align: center;
  margin: 0;
}

/* ===== TABLES ===== */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-xl);
  border: 1px solid var(--secondary-200);
  background: white;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--font-size-sm);
}

.table th,
.table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--secondary-200);
}

.table th {
  background-color: var(--secondary-50);
  font-weight: var(--font-weight-semibold);
  color: var(--secondary-700);
  position: sticky;
  top: 0;
}

.table tbody tr:hover {
  background-color: var(--secondary-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ===== TABS ===== */
.tabs {
  border-bottom: 1px solid var(--secondary-200);
  margin-bottom: var(--space-6);
}

.tab-list {
  display: flex;
  gap: var(--space-1);
  margin: 0;
  padding: 0;
  list-style: none;
}

.tab-button {
  padding: var(--space-4) var(--space-6);
  background: none;
  border: none;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--secondary-600);
  cursor: pointer;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  transition: all var(--transition-fast);
  position: relative;
}

.tab-button:hover {
  color: var(--primary-600);
  background-color: var(--secondary-50);
}

.tab-button.active {
  color: var(--primary-600);
  background-color: white;
  border-bottom: 2px solid var(--primary-600);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* ===== ALERTS ===== */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: 1px solid;
  margin-bottom: var(--space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.alert-success {
  background-color: var(--success-50);
  border-color: var(--success-200);
  color: var(--success-800);
}

.alert-warning {
  background-color: var(--warning-50);
  border-color: var(--warning-200);
  color: var(--warning-800);
}

.alert-error {
  background-color: var(--error-50);
  border-color: var(--error-200);
  color: var(--error-800);
}

.alert-icon {
  font-size: var(--font-size-lg);
  flex-shrink: 0;
  margin-top: var(--space-1);
}

/* ===== LOADING STATES ===== */
.loading {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--secondary-300);
  border-top: 2px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-3);
  }

  .nav-cards {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .theme-toggle {
    top: var(--space-3);
    right: var(--space-3);
  }

  .table-container {
    font-size: var(--font-size-xs);
  }

  .tab-button {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-sm);
  }
}
