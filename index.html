<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Work Management System</title>

    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        /* Custom styles for landing page */
        body {
            background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-4);
        }

        .hero-container {
            background: white;
            padding: var(--space-12);
            border-radius: var(--radius-2xl);
            box-shadow: var(--shadow-xl);
            text-align: center;
            width: 100%;
            max-width: 800px;
            position: relative;
        }

        .hero-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-4);
            background: linear-gradient(135deg, var(--primary-600), var(--primary-800));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-subtitle {
            font-size: var(--font-size-lg);
            color: var(--secondary-600);
            margin-bottom: var(--space-12);
            line-height: var(--line-height-relaxed);
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .nav-card-icon {
            color: var(--primary-600);
        }

        .nav-card:nth-child(1) .nav-card-icon {
            color: var(--success-600);
        }

        .nav-card:nth-child(2) .nav-card-icon {
            color: var(--warning-600);
        }

        .hero-footer {
            margin-top: var(--space-12);
            padding-top: var(--space-8);
            border-top: 1px solid var(--secondary-200);
            color: var(--secondary-600);
            font-size: var(--font-size-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-2);
        }

        .hero-footer i {
            color: var(--primary-600);
        }

        /* Floating animation for cards */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .nav-card:nth-child(1) {
            animation: float 6s ease-in-out infinite;
            animation-delay: 0s;
        }

        .nav-card:nth-child(2) {
            animation: float 6s ease-in-out infinite;
            animation-delay: 2s;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-container {
                padding: var(--space-8);
                margin: var(--space-4);
            }

            .hero-title {
                font-size: var(--font-size-3xl);
            }

            .hero-subtitle {
                font-size: var(--font-size-base);
            }
        }

        @media (max-width: 480px) {
            body {
                padding: var(--space-2);
            }

            .hero-container {
                padding: var(--space-6);
                margin: 0;
            }

            .hero-title {
                font-size: var(--font-size-2xl);
            }
        }
    </style>
</head>
<body>
    <div class="hero-container">
        <h1 class="hero-title">Daily Work Management</h1>
        <p class="hero-subtitle">Professional tools for tracking daily tasks and device upgrade requests with modern design and powerful features</p>

        <div class="nav-cards">
            <a href="todo-list.html" class="nav-card">
                <i class="nav-card-icon fas fa-clipboard-list"></i>
                <h3 class="nav-card-title">Daily To-Do List</h3>
                <p class="nav-card-description">Track your daily tasks, problems solved, and performance impact. Perfect for team reporting and productivity tracking.</p>
            </a>

            <a href="pc-inventory.html" class="nav-card">
                <i class="nav-card-icon fas fa-desktop"></i>
                <h3 class="nav-card-title">Device Management</h3>
                <p class="nav-card-description">Manage PC inventory and create upgrade requests with detailed justifications and approval workflows.</p>
            </a>

            <a href="dashboard.html" class="nav-card">
                <i class="nav-card-icon fas fa-chart-line"></i>
                <h3 class="nav-card-title">Analytics Dashboard</h3>
                <p class="nav-card-description">Comprehensive insights, reports, and performance metrics with interactive charts and data visualization.</p>
            </a>

            <a href="manual-report.html" class="nav-card">
                <i class="nav-card-icon fas fa-file-alt"></i>
                <h3 class="nav-card-title">Manual Report Generator</h3>
                <p class="nav-card-description">Create custom reports with problem descriptions, solutions, and professional signatures for documentation.</p>
            </a>
        </div>

        <div class="hero-footer">
            <i class="fas fa-shield-alt"></i>
            <p>Professional work tracking system with printable reports and signature approval workflow</p>
        </div>
    </div>

    <!-- Design System JavaScript -->
    <script src="assets/js/design-system.js"></script>
</body>
</html>



