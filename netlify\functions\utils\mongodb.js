// Load environment variables from .env file
require('dotenv').config();

const { MongoClient } = require('mongodb');

let cachedClient = null;
let cachedDb = null;

async function connectToDatabase() {
  if (cachedClient && cachedDb) {
    return { client: cachedClient, db: cachedDb };
  }

  const client = new MongoClient(process.env.MONGODB_URI);
  await client.connect();
  
  const db = client.db('pc-inventory');
  
  cachedClient = client;
  cachedDb = db;
  
  return { client, db };
}

module.exports = { connectToDatabase };