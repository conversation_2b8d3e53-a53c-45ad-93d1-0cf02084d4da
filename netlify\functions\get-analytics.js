const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    if (event.httpMethod !== 'GET') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ error: 'Method not allowed' })
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        
        // Parse query parameters
        const params = new URLSearchParams(event.queryString || '');
        const type = params.get('type') || 'overview';
        const period = params.get('period') || '30d';
        const dateFrom = params.get('dateFrom');
        const dateTo = params.get('dateTo');

        let analytics = {};

        switch (type) {
            case 'overview':
                analytics = await getOverviewAnalytics(db, period);
                break;
            case 'tasks':
                analytics = await getTaskAnalytics(db, period, dateFrom, dateTo);
                break;
            case 'equipment':
                analytics = await getEquipmentAnalytics(db, period);
                break;
            case 'productivity':
                analytics = await getProductivityAnalytics(db, period);
                break;
            default:
                analytics = await getOverviewAnalytics(db, period);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(analytics)
        };

    } catch (error) {
        console.error('Error getting analytics:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ error: 'Failed to get analytics data' })
        };
    } finally {
        await client.close();
    }
};

async function getOverviewAnalytics(db, period) {
    const now = new Date();
    const periodStart = getPeriodStart(now, period);

    // Get collections
    const todosCollection = db.collection('todos');
    const equipmentCollection = db.collection('equipment');
    const upgradesCollection = db.collection('upgrades');

    // Parallel queries for better performance
    const [
        totalTasks,
        completedTasks,
        recentTasks,
        totalEquipment,
        totalUpgrades,
        tasksByDay
    ] = await Promise.all([
        todosCollection.countDocuments(),
        todosCollection.countDocuments({ completed: true }),
        todosCollection.countDocuments({ 
            createdAt: { $gte: periodStart.toISOString() } 
        }),
        equipmentCollection.countDocuments(),
        upgradesCollection.countDocuments(),
        getTasksByDay(todosCollection, periodStart, now)
    ]);

    // Calculate metrics
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
    const productivityScore = calculateProductivityScore(completedTasks, totalTasks, recentTasks);
    const avgResponseTime = await calculateAverageResponseTime(todosCollection, periodStart);

    return {
        summary: {
            totalTasks,
            completedTasks,
            totalEquipment,
            totalUpgrades,
            completionRate: Math.round(completionRate),
            productivityScore,
            avgResponseTime
        },
        trends: {
            tasksByDay,
            period
        },
        lastUpdated: new Date().toISOString()
    };
}

async function getTaskAnalytics(db, period, dateFrom, dateTo) {
    const todosCollection = db.collection('todos');
    
    let startDate, endDate;
    if (dateFrom && dateTo) {
        startDate = new Date(dateFrom);
        endDate = new Date(dateTo);
    } else {
        endDate = new Date();
        startDate = getPeriodStart(endDate, period);
    }

    // Task completion trends
    const tasksByDay = await getTasksByDay(todosCollection, startDate, endDate);
    
    // Task categories analysis
    const taskCategories = await analyzeTaskCategories(todosCollection, startDate, endDate);
    
    // Performance metrics
    const performanceMetrics = await getTaskPerformanceMetrics(todosCollection, startDate, endDate);
    
    // Top issues/problems
    const topIssues = await getTopIssues(todosCollection, startDate, endDate);

    return {
        trends: {
            tasksByDay,
            taskCategories,
            period: `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`
        },
        performance: performanceMetrics,
        insights: {
            topIssues,
            recommendations: generateTaskRecommendations(performanceMetrics, taskCategories)
        },
        lastUpdated: new Date().toISOString()
    };
}

async function getEquipmentAnalytics(db, period) {
    const equipmentCollection = db.collection('equipment');
    const upgradesCollection = db.collection('upgrades');
    
    const endDate = new Date();
    const startDate = getPeriodStart(endDate, period);

    // Equipment status distribution
    const statusDistribution = await equipmentCollection.aggregate([
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 }
            }
        }
    ]).toArray();

    // Equipment by type
    const equipmentByType = await equipmentCollection.aggregate([
        {
            $group: {
                _id: '$type',
                count: { $sum: 1 }
            }
        }
    ]).toArray();

    // Upgrade requests analysis
    const upgradeAnalysis = await analyzeUpgradeRequests(upgradesCollection, startDate, endDate);

    // Equipment age analysis
    const ageAnalysis = await analyzeEquipmentAge(equipmentCollection);

    return {
        distribution: {
            byStatus: statusDistribution,
            byType: equipmentByType
        },
        upgrades: upgradeAnalysis,
        ageAnalysis,
        insights: {
            recommendations: generateEquipmentRecommendations(statusDistribution, ageAnalysis)
        },
        lastUpdated: new Date().toISOString()
    };
}

async function getProductivityAnalytics(db, period) {
    const todosCollection = db.collection('todos');
    
    const endDate = new Date();
    const startDate = getPeriodStart(endDate, period);

    // Daily productivity metrics
    const dailyProductivity = await getDailyProductivity(todosCollection, startDate, endDate);
    
    // Task completion patterns
    const completionPatterns = await getCompletionPatterns(todosCollection, startDate, endDate);
    
    // Efficiency metrics
    const efficiencyMetrics = await getEfficiencyMetrics(todosCollection, startDate, endDate);

    return {
        daily: dailyProductivity,
        patterns: completionPatterns,
        efficiency: efficiencyMetrics,
        insights: {
            recommendations: generateProductivityRecommendations(dailyProductivity, efficiencyMetrics)
        },
        lastUpdated: new Date().toISOString()
    };
}

// Helper functions
function getPeriodStart(endDate, period) {
    const start = new Date(endDate);
    switch (period) {
        case '7d':
            start.setDate(start.getDate() - 7);
            break;
        case '30d':
            start.setDate(start.getDate() - 30);
            break;
        case '90d':
            start.setDate(start.getDate() - 90);
            break;
        case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
        default:
            start.setDate(start.getDate() - 30);
    }
    return start;
}

async function getTasksByDay(collection, startDate, endDate) {
    const pipeline = [
        {
            $match: {
                createdAt: {
                    $gte: startDate.toISOString(),
                    $lte: endDate.toISOString()
                }
            }
        },
        {
            $group: {
                _id: {
                    $dateToString: {
                        format: "%Y-%m-%d",
                        date: { $dateFromString: { dateString: "$createdAt" } }
                    }
                },
                total: { $sum: 1 },
                completed: {
                    $sum: { $cond: [{ $eq: ["$completed", true] }, 1, 0] }
                }
            }
        },
        { $sort: { _id: 1 } }
    ];

    return await collection.aggregate(pipeline).toArray();
}

function calculateProductivityScore(completed, total, recent) {
    if (total === 0) return 0;
    
    const completionRate = (completed / total) * 100;
    const recentActivity = recent > 0 ? Math.min(recent * 10, 30) : 0;
    
    return Math.min(Math.round(completionRate + recentActivity), 100);
}

async function calculateAverageResponseTime(collection, startDate) {
    // Mock calculation - in real implementation, track actual response times
    const recentTasks = await collection.find({
        createdAt: { $gte: startDate.toISOString() }
    }).toArray();
    
    if (recentTasks.length === 0) return '0h';
    
    // Simulate response time calculation
    const avgHours = Math.random() * 4 + 1; // 1-5 hours
    return `${avgHours.toFixed(1)}h`;
}

async function analyzeTaskCategories(collection, startDate, endDate) {
    const tasks = await collection.find({
        createdAt: {
            $gte: startDate.toISOString(),
            $lte: endDate.toISOString()
        }
    }).toArray();

    const categories = {
        'Technical Issues': 0,
        'Maintenance': 0,
        'User Support': 0,
        'System Updates': 0,
        'Other': 0
    };

    tasks.forEach(task => {
        const content = ((task.task || '') + ' ' + (task.problem || '') + ' ' + (task.impact || '')).toLowerCase();
        
        if (content.includes('technical') || content.includes('bug') || content.includes('error')) {
            categories['Technical Issues']++;
        } else if (content.includes('maintenance') || content.includes('update') || content.includes('patch')) {
            categories['Maintenance']++;
        } else if (content.includes('user') || content.includes('support') || content.includes('help')) {
            categories['User Support']++;
        } else if (content.includes('system') || content.includes('server') || content.includes('network')) {
            categories['System Updates']++;
        } else {
            categories['Other']++;
        }
    });

    return Object.entries(categories).map(([name, count]) => ({ name, count }));
}

function generateTaskRecommendations(performance, categories) {
    const recommendations = [];
    
    // Add recommendations based on data analysis
    if (performance && performance.completionRate < 70) {
        recommendations.push('Consider breaking down large tasks into smaller, manageable items');
    }
    
    const topCategory = categories.reduce((max, cat) => cat.count > max.count ? cat : max, { count: 0 });
    if (topCategory.count > 0) {
        recommendations.push(`Focus on improving processes for ${topCategory.name} - your most common task type`);
    }
    
    return recommendations;
}

function generateEquipmentRecommendations(statusDist, ageAnalysis) {
    const recommendations = [];
    
    const maintenanceItems = statusDist.find(s => s._id === 'Maintenance');
    if (maintenanceItems && maintenanceItems.count > 0) {
        recommendations.push(`${maintenanceItems.count} items need maintenance attention`);
    }
    
    return recommendations;
}

function generateProductivityRecommendations(daily, efficiency) {
    return [
        'Consider scheduling regular breaks to maintain productivity',
        'Focus on completing tasks during your most productive hours'
    ];
}
