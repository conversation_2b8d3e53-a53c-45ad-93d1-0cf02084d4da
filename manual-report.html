<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manual Report Generator - Work Management System</title>
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="assets/css/design-system.css">
    
    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        /* Custom styles for manual report page */
        body {
            background: linear-gradient(135deg, var(--secondary-50) 0%, var(--secondary-100) 100%);
            min-height: 100vh;
            padding: var(--space-4);
        }

        .report-container {
            max-width: 900px;
            margin: 0 auto;
        }

        .report-header {
            text-align: center;
            margin-bottom: var(--space-8);
        }

        .report-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--space-3);
        }

        .report-title i {
            color: var(--primary-600);
        }

        .report-subtitle {
            font-size: var(--font-size-lg);
            color: var(--secondary-600);
            font-weight: var(--font-weight-medium);
        }

        .back-link {
            position: absolute;
            top: var(--space-4);
            left: var(--space-4);
            display: inline-flex;
            align-items: center;
            gap: var(--space-2);
            color: var(--secondary-600);
            text-decoration: none;
            font-weight: var(--font-weight-medium);
            transition: color var(--transition-fast);
        }

        .back-link:hover {
            color: var(--primary-600);
        }

        .report-form-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
            margin-bottom: var(--space-6);
        }

        .form-section {
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-6);
            border-bottom: 1px solid var(--secondary-200);
        }

        .form-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-6);
        }

        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-lg);
            color: white;
        }

        .section-icon.problem {
            background: var(--error-600);
        }

        .section-icon.solution {
            background: var(--success-600);
        }

        .section-icon.description {
            background: var(--primary-600);
        }

        .section-icon.signature {
            background: var(--warning-600);
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
        }

        .section-subtitle {
            font-size: var(--font-size-sm);
            color: var(--secondary-600);
            margin: 0;
        }

        .rich-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: var(--font-family-sans);
            line-height: var(--line-height-relaxed);
        }

        .signature-section {
            background: var(--secondary-50);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-top: var(--space-6);
        }

        .signature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-8);
            margin-top: var(--space-6);
        }

        @media (max-width: 768px) {
            .signature-grid {
                grid-template-columns: 1fr;
                gap: var(--space-6);
            }
        }

        .signature-field {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .signature-label {
            font-weight: var(--font-weight-semibold);
            color: var(--secondary-700);
            font-size: var(--font-size-sm);
        }

        .signature-line {
            border-bottom: 2px solid var(--secondary-400);
            height: 50px;
            background: white;
            border-radius: var(--radius-sm) var(--radius-sm) 0 0;
            position: relative;
            display: flex;
            align-items: end;
            padding: var(--space-2);
        }

        .signature-input {
            border: none;
            background: transparent;
            width: 100%;
            font-family: 'Brush Script MT', cursive;
            font-size: var(--font-size-lg);
            color: var(--secondary-800);
            outline: none;
        }

        .date-field {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            margin-top: var(--space-2);
        }

        .date-input {
            flex: 1;
            max-width: 150px;
        }

        .report-actions {
            display: flex;
            justify-content: center;
            gap: var(--space-4);
            margin-top: var(--space-8);
            flex-wrap: wrap;
        }

        .report-preview {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-8);
            box-shadow: var(--shadow-md);
            border: 1px solid var(--secondary-200);
            margin-top: var(--space-6);
            display: none;
        }

        .report-preview.active {
            display: block;
        }

        .preview-header {
            text-align: center;
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-4);
            border-bottom: 2px solid var(--secondary-300);
        }

        .preview-title {
            font-size: var(--font-size-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-2);
        }

        .preview-date {
            font-size: var(--font-size-base);
            color: var(--secondary-600);
        }

        .preview-section {
            margin-bottom: var(--space-8);
        }

        .preview-section-title {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-2);
            border-bottom: 1px solid var(--secondary-200);
        }

        .preview-content {
            color: var(--secondary-700);
            line-height: var(--line-height-relaxed);
            white-space: pre-wrap;
        }

        .preview-signatures {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-8);
            margin-top: var(--space-12);
            padding-top: var(--space-6);
            border-top: 1px solid var(--secondary-200);
        }

        .preview-signature {
            text-align: center;
        }

        .preview-signature-line {
            border-bottom: 2px solid var(--secondary-400);
            height: 60px;
            margin-bottom: var(--space-2);
            display: flex;
            align-items: end;
            justify-content: center;
            padding-bottom: var(--space-2);
        }

        .preview-signature-text {
            font-family: 'Brush Script MT', cursive;
            font-size: var(--font-size-xl);
            color: var(--secondary-800);
        }

        .preview-signature-label {
            font-weight: var(--font-weight-medium);
            color: var(--secondary-700);
            margin-bottom: var(--space-1);
        }

        .preview-signature-date {
            font-size: var(--font-size-sm);
            color: var(--secondary-600);
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .back-link,
            .report-form-card,
            .report-actions,
            .theme-toggle {
                display: none;
            }
            
            .report-preview {
                display: block !important;
                box-shadow: none;
                border: none;
                margin: 0;
                padding: 40px;
            }
            
            .preview-signatures {
                page-break-inside: avoid;
            }
        }

        /* Loading and success states */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: var(--z-modal);
            display: none;
        }

        .loading-overlay.active {
            display: flex;
        }

        .success-message {
            background: var(--success-50);
            border: 1px solid var(--success-200);
            color: var(--success-800);
            padding: var(--space-4);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-6);
            display: none;
        }

        .success-message.active {
            display: block;
        }

        /* Multiple Problems Styles */
        .problems-hint {
            margin-bottom: var(--space-6);
        }

        .problem-entry {
            background: var(--secondary-50);
            border: 1px solid var(--secondary-200);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            position: relative;
            transition: all var(--transition-normal);
        }

        .problem-entry:hover {
            box-shadow: var(--shadow-sm);
        }

        .problem-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-4);
            padding-bottom: var(--space-3);
            border-bottom: 1px solid var(--secondary-300);
        }

        .problem-number {
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-700);
        }

        .problem-badge {
            background: var(--primary-600);
            color: white;
            width: 28px;
            height: 28px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-bold);
        }

        .problem-actions {
            display: flex;
            gap: var(--space-2);
        }

        .remove-problem-btn {
            background: var(--error-500);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-3);
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: var(--font-size-sm);
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-1);
        }

        .remove-problem-btn:hover {
            background: var(--error-600);
            transform: translateY(-1px);
        }

        .problem-fields {
            display: grid;
            gap: var(--space-4);
        }

        .problem-name-field {
            grid-column: 1 / -1;
        }

        .problem-description-field {
            grid-column: 1 / -1;
        }

        .problem-solution-field {
            grid-column: 1 / -1;
        }

        .problem-additional-field {
            grid-column: 1 / -1;
        }

        .problem-textarea {
            min-height: 100px;
            resize: vertical;
        }

        .problem-name-input {
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-lg);
        }

        /* Empty state for problems */
        .no-problems {
            text-align: center;
            padding: var(--space-8);
            color: var(--secondary-500);
            border: 2px dashed var(--secondary-300);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-6);
        }

        .no-problems i {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--space-3);
            color: var(--secondary-400);
        }

        /* Preview styles for multiple problems */
        .preview-problems {
            margin-bottom: var(--space-8);
        }

        .preview-problem {
            margin-bottom: var(--space-8);
            padding-bottom: var(--space-6);
            border-bottom: 1px solid var(--secondary-200);
        }

        .preview-problem:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .preview-problem-header {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            margin-bottom: var(--space-4);
        }

        .preview-problem-number {
            background: var(--primary-600);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: var(--font-weight-bold);
            font-size: var(--font-size-sm);
        }

        .preview-problem-name {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-bold);
            color: var(--secondary-900);
            margin: 0;
        }

        .preview-problem-section {
            margin-bottom: var(--space-4);
        }

        .preview-problem-section:last-child {
            margin-bottom: 0;
        }

        .preview-problem-section-title {
            font-size: var(--font-size-base);
            font-weight: var(--font-weight-semibold);
            color: var(--secondary-800);
            margin-bottom: var(--space-2);
        }

        .preview-problem-content {
            color: var(--secondary-700);
            line-height: var(--line-height-relaxed);
            white-space: pre-wrap;
            margin-left: var(--space-4);
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">
        <i class="fas fa-arrow-left"></i>
        Back to Dashboard
    </a>
    
    <div class="report-container">


        <div class="success-message" id="successMessage">
            <i class="fas fa-check-circle"></i>
            Report saved successfully!
        </div>

        <div class="report-form-card">
            <form id="reportForm">
                <!-- Report Metadata -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon" style="background: var(--secondary-600);">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Report Information</h3>
                            <p class="section-subtitle">Basic report details and metadata</p>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reportTitle" class="form-label">Report Title *</label>
                            <input type="text" id="reportTitle" class="form-input" placeholder="Enter report title..." required>
                        </div>
                        <div class="form-group">
                            <label for="reportDate" class="form-label">Report Date *</label>
                            <input type="date" id="reportDate" class="form-input" required>
                        </div>
                    </div>
                </div>

                <!-- Problems Section -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon problem">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Problems & Solutions</h3>
                            <p class="section-subtitle">Add multiple problems with individual descriptions, solutions, and details</p>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" onclick="addProblem()" style="margin-left: auto;">
                            <i class="fas fa-plus"></i>
                            Add Problem
                        </button>
                    </div>

                    <div class="problems-hint">
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb"></i>
                            <div>
                                <strong>Multiple Problems Support:</strong> You can add multiple problems to this report. Each problem can have its own name, description, solution, and additional details.
                                <div style="margin-top: var(--space-2); font-size: var(--font-size-sm);">
                                    Click "Add Problem" to create additional problem entries. At least one problem is required.
                                </div>
                            </div>
                        </div>
                    </div>

                    <div id="problemsContainer">
                        <!-- Problems will be dynamically added here -->
                    </div>
                </div>

                <!-- Signature Section -->
                <div class="form-section">
                    <div class="section-header">
                        <div class="section-icon signature">
                            <i class="fas fa-signature"></i>
                        </div>
                        <div>
                            <h3 class="section-title">Signatures & Approval</h3>
                            <p class="section-subtitle">Required signatures for report validation and approval</p>
                        </div>
                    </div>
                    
                    <div class="signature-section">
                        <div class="signature-grid">
                            <div class="signature-field">
                                <label class="signature-label">Employee Signature</label>
                                <div class="signature-line">
                                    <input type="text" class="signature-input" id="employeeSignature" placeholder="Type your name here">
                                </div>
                                <div class="date-field">
                                    <span class="signature-label">Date:</span>
                                    <input type="date" class="form-input date-input" id="employeeDate">
                                </div>
                            </div>
                            
                            <div class="signature-field">
                                <label class="signature-label">Supervisor Signature</label>
                                <div class="signature-line">
                                    <input type="text" class="signature-input" id="supervisorSignature" placeholder="Supervisor name">
                                </div>
                                <div class="date-field">
                                    <span class="signature-label">Date:</span>
                                    <input type="date" class="form-input date-input" id="supervisorDate">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="report-actions">
            <button type="button" class="btn btn-secondary" onclick="previewReport()">
                <i class="fas fa-eye"></i>
                Preview Report
            </button>
            <button type="button" class="btn btn-primary" onclick="saveReport()">
                <i class="fas fa-save"></i>
                Save Report
            </button>
            <button type="button" class="btn btn-success" onclick="printReport()">
                <i class="fas fa-print"></i>
                Print Report
            </button>
            <button type="button" class="btn btn-warning" onclick="exportToPDF()">
                <i class="fas fa-file-pdf"></i>
                Export PDF
            </button>
        </div>

        <!-- Report Preview -->
        <div class="report-preview" id="reportPreview">
            <div class="preview-header">
                <h1 class="preview-title" id="previewTitle">Report Title</h1>
                <p class="preview-date" id="previewDate">Date: </p>
            </div>

            <div class="preview-section">
                <h2 class="preview-section-title">Problems & Solutions</h2>
                <div class="preview-problems" id="previewProblems">
                    <!-- Problems will be dynamically populated here -->
                </div>
            </div>

            <div class="preview-signatures">
                <div class="preview-signature">
                    <div class="preview-signature-label">Employee Signature</div>
                    <div class="preview-signature-line">
                        <span class="preview-signature-text" id="previewEmployeeSignature"></span>
                    </div>
                    <div class="preview-signature-date" id="previewEmployeeDate">Date: </div>
                </div>
                
                <div class="preview-signature">
                    <div class="preview-signature-label">Supervisor Signature</div>
                    <div class="preview-signature-line">
                        <span class="preview-signature-text" id="previewSupervisorSignature"></span>
                    </div>
                    <div class="preview-signature-date" id="previewSupervisorDate">Date: </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading">
            <div class="spinner"></div>
            <span>Processing report...</span>
        </div>
    </div>

    <!-- Design System JavaScript -->
    <script src="assets/js/design-system.js"></script>

    <script>
        // Manual Report Generator JavaScript
        class ManualReportGenerator {
            constructor() {
                this.API_BASE = '/.netlify/functions';
                this.currentReport = null;
                this.problems = [];
                this.problemCounter = 0;
                this.init();
            }

            init() {
                this.setupFormDefaults();
                this.setupEventListeners();
                this.addProblem(); // Add first problem by default
                this.loadDraftReport();
            }

            setupFormDefaults() {
                // Set default date to today
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('reportDate').value = today;
                document.getElementById('employeeDate').value = today;
                document.getElementById('supervisorDate').value = today;
            }

            setupEventListeners() {
                // Auto-save draft as user types (delegate to handle dynamic content)
                document.addEventListener('input', (e) => {
                    if (e.target.closest('#reportForm')) {
                        this.saveDraftReport();
                    }
                });

                // Form validation
                document.getElementById('reportForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.saveReport();
                });
            }

            addProblem() {
                this.problemCounter++;
                const problemId = `problem_${this.problemCounter}`;

                const problemEntry = {
                    id: problemId,
                    number: this.problemCounter,
                    name: '',
                    description: '',
                    solution: '',
                    additional: ''
                };

                this.problems.push(problemEntry);
                this.renderProblems();

                // Focus on the new problem name field
                setTimeout(() => {
                    const nameField = document.getElementById(`${problemId}_name`);
                    if (nameField) nameField.focus();
                }, 100);
            }

            removeProblem(problemId) {
                if (this.problems.length <= 1) {
                    this.showAlert('At least one problem is required', 'warning');
                    return;
                }

                this.problems = this.problems.filter(p => p.id !== problemId);
                this.renderProblems();
                this.saveDraftReport();
            }

            renderProblems() {
                const container = document.getElementById('problemsContainer');

                if (this.problems.length === 0) {
                    container.innerHTML = `
                        <div class="no-problems">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h3>No Problems Added</h3>
                            <p>Click "Add Problem" to create your first problem entry.</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = this.problems.map((problem, index) => `
                    <div class="problem-entry" data-problem-id="${problem.id}">
                        <div class="problem-header">
                            <div class="problem-number">
                                <div class="problem-badge">${index + 1}</div>
                                <span>Problem ${index + 1}</span>
                            </div>
                            <div class="problem-actions">
                                ${this.problems.length > 1 ? `
                                    <button type="button" class="remove-problem-btn" onclick="reportGenerator.removeProblem('${problem.id}')">
                                        <i class="fas fa-trash"></i>
                                        Remove
                                    </button>
                                ` : ''}
                            </div>
                        </div>

                        <div class="problem-fields">
                            <div class="form-group problem-name-field">
                                <label for="${problem.id}_name" class="form-label">Problem Name *</label>
                                <input type="text" id="${problem.id}_name" class="form-input problem-name-input"
                                       placeholder="Enter a descriptive name for this problem..."
                                       value="${problem.name}" required>
                            </div>

                            <div class="form-group problem-description-field">
                                <label for="${problem.id}_description" class="form-label">Problem Description *</label>
                                <textarea id="${problem.id}_description" class="form-textarea problem-textarea"
                                          placeholder="Describe the problem in detail. Include what happened, when it occurred, who was affected, and any relevant circumstances..."
                                          required>${problem.description}</textarea>
                            </div>

                            <div class="form-group problem-solution-field">
                                <label for="${problem.id}_solution" class="form-label">Solution & Fix Applied *</label>
                                <textarea id="${problem.id}_solution" class="form-textarea problem-textarea"
                                          placeholder="Describe the solution implemented. Include specific steps taken, tools used, configurations changed, and the resolution process..."
                                          required>${problem.solution}</textarea>
                            </div>

                            <div class="form-group problem-additional-field">
                                <label for="${problem.id}_additional" class="form-label">Additional Details</label>
                                <textarea id="${problem.id}_additional" class="form-textarea problem-textarea"
                                          placeholder="Include any additional relevant information such as impact on operations, lessons learned, preventive measures, or recommendations for future...">${problem.additional}</textarea>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            updateProblemData() {
                this.problems.forEach(problem => {
                    const nameField = document.getElementById(`${problem.id}_name`);
                    const descField = document.getElementById(`${problem.id}_description`);
                    const solutionField = document.getElementById(`${problem.id}_solution`);
                    const additionalField = document.getElementById(`${problem.id}_additional`);

                    if (nameField) problem.name = nameField.value.trim();
                    if (descField) problem.description = descField.value.trim();
                    if (solutionField) problem.solution = solutionField.value.trim();
                    if (additionalField) problem.additional = additionalField.value.trim();
                });
            }

            previewReport() {
                this.updateProblemData();
                const formData = this.getFormData();

                if (!this.validateRequiredFields(formData)) {
                    this.showAlert('Please fill in all required fields before previewing', 'warning');
                    return;
                }

                this.updatePreview(formData);
                document.getElementById('reportPreview').classList.add('active');

                // Scroll to preview
                document.getElementById('reportPreview').scrollIntoView({
                    behavior: 'smooth'
                });
            }

            updatePreview(data) {
                document.getElementById('previewTitle').textContent = data.title;
                document.getElementById('previewDate').textContent = `Date: ${new Date(data.date).toLocaleDateString()}`;

                // Update problems preview
                const problemsContainer = document.getElementById('previewProblems');
                if (data.problems && data.problems.length > 0) {
                    problemsContainer.innerHTML = data.problems.map((problem, index) => `
                        <div class="preview-problem">
                            <div class="preview-problem-header">
                                <div class="preview-problem-number">${index + 1}</div>
                                <h3 class="preview-problem-name">${problem.name || `Problem ${index + 1}`}</h3>
                            </div>

                            <div class="preview-problem-section">
                                <div class="preview-problem-section-title">Problem Description</div>
                                <div class="preview-problem-content">${problem.description}</div>
                            </div>

                            <div class="preview-problem-section">
                                <div class="preview-problem-section-title">Solution & Fix Applied</div>
                                <div class="preview-problem-content">${problem.solution}</div>
                            </div>

                            ${problem.additional ? `
                            <div class="preview-problem-section">
                                <div class="preview-problem-section-title">Additional Details</div>
                                <div class="preview-problem-content">${problem.additional}</div>
                            </div>
                            ` : ''}
                        </div>
                    `).join('');
                } else {
                    problemsContainer.innerHTML = '<p style="color: #666; font-style: italic;">No problems added.</p>';
                }

                // Update signatures
                document.getElementById('previewEmployeeSignature').textContent = data.employeeSignature || '';
                document.getElementById('previewEmployeeDate').textContent = `Date: ${data.employeeDate ? new Date(data.employeeDate).toLocaleDateString() : ''}`;
                document.getElementById('previewSupervisorSignature').textContent = data.supervisorSignature || '';
                document.getElementById('previewSupervisorDate').textContent = `Date: ${data.supervisorDate ? new Date(data.supervisorDate).toLocaleDateString() : ''}`;
            }

            async saveReport() {
                this.updateProblemData();
                const formData = this.getFormData();

                if (!this.validateRequiredFields(formData)) {
                    this.showAlert('Please fill in all required fields', 'error');
                    return;
                }

                this.showLoading(true);

                try {
                    const response = await fetch(`${this.API_BASE}/save-manual-report`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            ...formData,
                            createdAt: new Date().toISOString(),
                            type: 'manual_report'
                        })
                    });

                    if (response.ok) {
                        const result = await response.json();
                        this.currentReport = result.report;
                        this.showSuccessMessage();
                        this.clearDraftReport();
                        this.showAlert('Report saved successfully!', 'success');
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                } catch (error) {
                    console.error('Error saving report:', error);
                    this.showAlert('Error saving report. Please try again.', 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            printReport() {
                this.updateProblemData();
                const formData = this.getFormData();

                if (!this.validateRequiredFields(formData)) {
                    this.showAlert('Please fill in all required fields before printing', 'warning');
                    return;
                }

                this.updatePreview(formData);
                document.getElementById('reportPreview').classList.add('active');

                // Small delay to ensure preview is rendered
                setTimeout(() => {
                    window.print();
                }, 100);
            }

            async exportToPDF() {
                this.updateProblemData();
                const formData = this.getFormData();

                if (!this.validateRequiredFields(formData)) {
                    this.showAlert('Please fill in all required fields before exporting', 'warning');
                    return;
                }

                this.showLoading(true);

                try {
                    // Update preview for PDF generation
                    this.updatePreview(formData);
                    document.getElementById('reportPreview').classList.add('active');

                    // Generate PDF using browser's print functionality
                    const printWindow = window.open('', '_blank');
                    const reportContent = document.getElementById('reportPreview').outerHTML;

                    printWindow.document.write(`
                        <html>
                        <head>
                            <title>${formData.title}</title>
                            <style>
                                body { font-family: Arial, sans-serif; padding: 40px; line-height: 1.6; }
                                .preview-header { text-align: center; margin-bottom: 40px; padding-bottom: 20px; border-bottom: 2px solid #333; }
                                .preview-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                                .preview-section { margin-bottom: 30px; }
                                .preview-section-title { font-size: 18px; font-weight: bold; margin-bottom: 15px; padding-bottom: 5px; border-bottom: 1px solid #ccc; }
                                .preview-content { white-space: pre-wrap; }
                                .preview-problem { margin-bottom: 40px; padding-bottom: 30px; border-bottom: 1px solid #ddd; }
                                .preview-problem:last-child { border-bottom: none; }
                                .preview-problem-header { display: flex; align-items: center; gap: 15px; margin-bottom: 20px; }
                                .preview-problem-number { background: #007bff; color: white; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; }
                                .preview-problem-name { font-size: 20px; font-weight: bold; margin: 0; }
                                .preview-problem-section { margin-bottom: 20px; }
                                .preview-problem-section-title { font-weight: bold; margin-bottom: 10px; }
                                .preview-problem-content { margin-left: 20px; white-space: pre-wrap; }
                                .preview-signatures { display: grid; grid-template-columns: 1fr 1fr; gap: 40px; margin-top: 60px; padding-top: 20px; border-top: 1px solid #ccc; }
                                .preview-signature { text-align: center; }
                                .preview-signature-line { border-bottom: 2px solid #333; height: 60px; margin-bottom: 10px; display: flex; align-items: end; justify-content: center; padding-bottom: 10px; }
                                .preview-signature-text { font-family: 'Brush Script MT', cursive; font-size: 20px; }
                                .preview-signature-label { font-weight: bold; margin-bottom: 5px; }
                            </style>
                        </head>
                        <body>${reportContent}</body>
                        </html>
                    `);

                    printWindow.document.close();
                    printWindow.focus();

                    setTimeout(() => {
                        printWindow.print();
                        printWindow.close();
                    }, 500);

                    this.showAlert('PDF export initiated. Please use your browser\'s print dialog to save as PDF.', 'info');
                } catch (error) {
                    console.error('Error exporting PDF:', error);
                    this.showAlert('Error exporting PDF. Please try again.', 'error');
                } finally {
                    this.showLoading(false);
                }
            }

            getFormData() {
                this.updateProblemData();

                return {
                    title: document.getElementById('reportTitle').value.trim(),
                    date: document.getElementById('reportDate').value,
                    problems: this.problems.filter(p => p.name || p.description || p.solution), // Only include non-empty problems
                    employeeSignature: document.getElementById('employeeSignature').value.trim(),
                    employeeDate: document.getElementById('employeeDate').value,
                    supervisorSignature: document.getElementById('supervisorSignature').value.trim(),
                    supervisorDate: document.getElementById('supervisorDate').value
                };
            }

            validateRequiredFields(data) {
                // Check basic required fields
                if (!data.title || !data.date) {
                    return false;
                }

                // Check that at least one problem has required fields
                if (!data.problems || data.problems.length === 0) {
                    return false;
                }

                // Validate each problem has required fields
                return data.problems.some(problem =>
                    problem.name && problem.name.trim() &&
                    problem.description && problem.description.trim() &&
                    problem.solution && problem.solution.trim()
                );
            }

            saveDraftReport() {
                const formData = this.getFormData();
                localStorage.setItem('manualReportDraft', JSON.stringify({
                    ...formData,
                    problemsData: this.problems, // Save the full problems array
                    lastSaved: new Date().toISOString()
                }));
            }

            loadDraftReport() {
                const draft = localStorage.getItem('manualReportDraft');
                if (draft) {
                    try {
                        const data = JSON.parse(draft);

                        // Only load if draft is less than 24 hours old
                        const lastSaved = new Date(data.lastSaved);
                        const now = new Date();
                        const hoursDiff = (now - lastSaved) / (1000 * 60 * 60);

                        if (hoursDiff < 24) {
                            this.populateForm(data);
                            this.showAlert('Draft report loaded', 'info');
                        } else {
                            this.clearDraftReport();
                        }
                    } catch (error) {
                        console.error('Error loading draft:', error);
                        this.clearDraftReport();
                    }
                }
            }

            populateForm(data) {
                // Populate basic fields
                document.getElementById('reportTitle').value = data.title || '';
                document.getElementById('reportDate').value = data.date || '';
                document.getElementById('employeeSignature').value = data.employeeSignature || '';
                document.getElementById('employeeDate').value = data.employeeDate || '';
                document.getElementById('supervisorSignature').value = data.supervisorSignature || '';
                document.getElementById('supervisorDate').value = data.supervisorDate || '';

                // Populate problems
                if (data.problemsData && data.problemsData.length > 0) {
                    this.problems = data.problemsData;
                    this.problemCounter = Math.max(...this.problems.map(p => parseInt(p.id.split('_')[1])));
                    this.renderProblems();
                } else if (data.problems && data.problems.length > 0) {
                    // Handle old format or direct problems data
                    this.problems = data.problems.map((problem, index) => ({
                        id: `problem_${index + 1}`,
                        number: index + 1,
                        name: problem.name || '',
                        description: problem.description || '',
                        solution: problem.solution || '',
                        additional: problem.additional || ''
                    }));
                    this.problemCounter = this.problems.length;
                    this.renderProblems();
                }
            }

            clearDraftReport() {
                localStorage.removeItem('manualReportDraft');
            }

            showLoading(show) {
                const overlay = document.getElementById('loadingOverlay');
                if (show) {
                    overlay.classList.add('active');
                } else {
                    overlay.classList.remove('active');
                }
            }

            showSuccessMessage() {
                const message = document.getElementById('successMessage');
                message.classList.add('active');
                setTimeout(() => {
                    message.classList.remove('active');
                }, 5000);
            }

            showAlert(message, type = 'info') {
                if (window.designSystemInstance) {
                    window.designSystemInstance.showAlert(message, type, {
                        autoDismiss: true,
                        dismissible: true
                    });
                } else {
                    alert(message);
                }
            }
        }

        // Global functions for button onclick events
        let reportGenerator;

        function addProblem() {
            reportGenerator.addProblem();
        }

        function previewReport() {
            reportGenerator.previewReport();
        }

        function saveReport() {
            reportGenerator.saveReport();
        }

        function printReport() {
            reportGenerator.printReport();
        }

        function exportToPDF() {
            reportGenerator.exportToPDF();
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            reportGenerator = new ManualReportGenerator();
        });
    </script>
