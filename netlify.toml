[build]
  # No build command needed for static HTML
  publish = "."
  functions = "netlify/functions"

[build.environment]
  NODE_VERSION = "18"

# Redirect rules for SPA-like behavior (optional)
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200

# Headers for CORS (if needed)
[[headers]]
  for = "/.netlify/functions/*"
  [headers.values]
    Access-Control-Allow-Origin = "*"
    Access-Control-Allow-Headers = "Content-Type"
    Access-Control-Allow-Methods = "GET, POST, PUT, DELETE, OPTIONS"
