const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        
        // Parse request parameters
        const params = new URLSearchParams(event.queryString || '');
        const mode = params.get('mode') || 'preview'; // preview, execute
        const daysBack = parseInt(params.get('daysBack')) || 30;
        const strategy = params.get('strategy') || 'smart'; // smart, all, completed
        
        let result;
        
        switch (mode) {
            case 'preview':
                result = await previewMigration(db, daysBack, strategy);
                break;
            case 'execute':
                result = await executeMigration(db, daysBack, strategy);
                break;
            default:
                result = await previewMigration(db, daysBack, strategy);
        }

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(result)
        };

    } catch (error) {
        console.error('Error in task migration:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to migrate existing tasks',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

async function previewMigration(db, daysBack, strategy) {
    const todosCollection = db.collection('todos');
    
    // Get all existing tasks
    const allTasks = await todosCollection.find({}).toArray();
    
    if (allTasks.length === 0) {
        return {
            preview: true,
            message: 'No existing tasks found to migrate',
            totalTasks: 0,
            migrationPlan: []
        };
    }
    
    // Analyze tasks and create migration plan
    const migrationPlan = createMigrationPlan(allTasks, daysBack, strategy);
    
    return {
        preview: true,
        totalTasks: allTasks.length,
        migrationPlan: migrationPlan,
        summary: {
            tasksToArchive: migrationPlan.reduce((sum, day) => sum + day.tasksToArchive, 0),
            tasksToKeep: migrationPlan.reduce((sum, day) => sum + day.tasksToKeep, 0),
            daysToProcess: migrationPlan.length
        }
    };
}

async function executeMigration(db, daysBack, strategy) {
    const todosCollection = db.collection('todos');
    const archivedTasksCollection = db.collection('archived_tasks');
    const migrationLogCollection = db.collection('migration_log');
    
    // Get all existing tasks
    const allTasks = await todosCollection.find({}).toArray();
    
    if (allTasks.length === 0) {
        return {
            success: true,
            message: 'No tasks to migrate',
            migrated: 0
        };
    }
    
    // Create migration plan
    const migrationPlan = createMigrationPlan(allTasks, daysBack, strategy);
    
    let totalMigrated = 0;
    const migrationResults = [];
    
    // Process each day in the migration plan
    for (const dayPlan of migrationPlan) {
        if (dayPlan.tasksToArchive === 0) continue;
        
        const tasksToMigrate = allTasks.filter(task => 
            dayPlan.taskIds.includes(task._id.toString())
        );
        
        // Prepare archived tasks with proper metadata
        const archivedTasks = tasksToMigrate.map(task => ({
            ...task,
            originalId: task._id,
            archivedAt: new Date().toISOString(),
            archiveDate: dayPlan.date,
            archiveReason: 'Data migration - historical task',
            isManualArchive: false,
            isMigrated: true,
            dayOfWeek: new Date(dayPlan.date).toLocaleDateString('en-US', { weekday: 'long' }),
            weekNumber: getWeekNumber(new Date(dayPlan.date)),
            monthYear: new Date(dayPlan.date).toLocaleDateString('en-US', { month: 'long', year: 'numeric' })
        }));
        
        // Perform migration for this day
        const session = client.startSession();
        
        try {
            await session.withTransaction(async () => {
                // Insert into archive
                await archivedTasksCollection.insertMany(archivedTasks, { session });
                
                // Remove from active todos
                const taskIds = tasksToMigrate.map(task => task._id);
                await todosCollection.deleteMany({ _id: { $in: taskIds } }, { session });
            });
            
            totalMigrated += archivedTasks.length;
            migrationResults.push({
                date: dayPlan.date,
                migrated: archivedTasks.length,
                success: true
            });
            
        } catch (error) {
            migrationResults.push({
                date: dayPlan.date,
                migrated: 0,
                success: false,
                error: error.message
            });
        } finally {
            await session.endSession();
        }
    }
    
    // Log the migration
    await migrationLogCollection.insertOne({
        timestamp: new Date().toISOString(),
        strategy: strategy,
        daysBack: daysBack,
        totalTasksProcessed: allTasks.length,
        totalMigrated: totalMigrated,
        results: migrationResults
    });
    
    return {
        success: true,
        message: `Successfully migrated ${totalMigrated} tasks`,
        totalMigrated: totalMigrated,
        remainingTasks: await todosCollection.countDocuments(),
        migrationResults: migrationResults
    };
}

function createMigrationPlan(tasks, daysBack, strategy) {
    const plan = [];
    const now = new Date();
    
    // Create a plan for each day going back
    for (let i = 1; i <= daysBack; i++) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        const dateString = date.toISOString().split('T')[0];
        
        // Find tasks that should be archived for this date
        const dayTasks = getTasksForDate(tasks, date, strategy);
        
        plan.push({
            date: dateString,
            tasksToArchive: dayTasks.length,
            tasksToKeep: 0, // Will be calculated later
            taskIds: dayTasks.map(task => task._id.toString()),
            strategy: strategy
        });
    }
    
    // Calculate tasks to keep (tasks not being archived)
    const allTasksToArchive = new Set();
    plan.forEach(dayPlan => {
        dayPlan.taskIds.forEach(id => allTasksToArchive.add(id));
    });
    
    const tasksToKeep = tasks.filter(task => 
        !allTasksToArchive.has(task._id.toString())
    ).length;
    
    // Update the plan with tasks to keep info
    if (plan.length > 0) {
        plan[0].tasksToKeep = tasksToKeep;
    }
    
    return plan.reverse(); // Return in chronological order
}

function getTasksForDate(tasks, date, strategy) {
    const dateString = date.toISOString().split('T')[0];
    
    switch (strategy) {
        case 'smart':
            // Archive completed tasks and old incomplete tasks
            return tasks.filter(task => {
                if (task.completed) {
                    // Archive completed tasks that are older than the date
                    const taskDate = task.completedAt || task.createdAt;
                    return taskDate && taskDate.startsWith(dateString);
                } else {
                    // Archive incomplete tasks that are much older
                    const taskDate = task.createdAt;
                    if (!taskDate) return false;
                    
                    const taskCreatedDate = new Date(taskDate);
                    const daysDiff = (date - taskCreatedDate) / (1000 * 60 * 60 * 24);
                    return daysDiff >= 7; // Archive incomplete tasks older than 7 days
                }
            });
            
        case 'completed':
            // Only archive completed tasks
            return tasks.filter(task => {
                if (!task.completed) return false;
                const taskDate = task.completedAt || task.createdAt;
                return taskDate && taskDate.startsWith(dateString);
            });
            
        case 'all':
            // Archive all tasks created on this date
            return tasks.filter(task => {
                const taskDate = task.createdAt;
                return taskDate && taskDate.startsWith(dateString);
            });
            
        default:
            return [];
    }
}

function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// Utility function to check migration status
async function getMigrationStatus(db) {
    const migrationLogCollection = db.collection('migration_log');
    const todosCollection = db.collection('todos');
    const archivedTasksCollection = db.collection('archived_tasks');
    
    const [lastMigration, totalActive, totalArchived] = await Promise.all([
        migrationLogCollection.findOne({}, { sort: { timestamp: -1 } }),
        todosCollection.countDocuments(),
        archivedTasksCollection.countDocuments({ isMigrated: true })
    ]);
    
    return {
        hasMigrated: !!lastMigration,
        lastMigration: lastMigration?.timestamp,
        totalActiveTasks: totalActive,
        totalMigratedTasks: totalArchived,
        migrationStrategy: lastMigration?.strategy
    };
}
