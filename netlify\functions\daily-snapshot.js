const { MongoClient } = require('mongodb');

const uri = process.env.MONGODB_URI;
const client = new MongoClient(uri);

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS'
    };

    // <PERSON>le preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: ''
        };
    }

    try {
        await client.connect();
        const db = client.db('workmanagement');
        
        // Get current date for snapshot
        const snapshotDate = new Date();
        const dateString = snapshotDate.toISOString().split('T')[0]; // YYYY-MM-DD format
        
        // Check if snapshot already exists for today
        const existingSnapshot = await db.collection('daily_snapshots').findOne({
            date: dateString
        });

        if (existingSnapshot && event.httpMethod === 'GET') {
            return {
                statusCode: 200,
                headers,
                body: JSON.stringify({
                    message: 'Snapshot already exists for today',
                    snapshot: existingSnapshot
                })
            };
        }

        // Create daily snapshot
        const snapshot = await createDailySnapshot(db, snapshotDate, dateString);
        
        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({
                message: 'Daily snapshot created successfully',
                snapshot: snapshot
            })
        };

    } catch (error) {
        console.error('Error creating daily snapshot:', error);
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({ 
                error: 'Failed to create daily snapshot',
                details: error.message 
            })
        };
    } finally {
        await client.close();
    }
};

async function createDailySnapshot(db, snapshotDate, dateString) {
    // Get all collections
    const todosCollection = db.collection('todos');
    const archivedTasksCollection = db.collection('archived_tasks');
    const equipmentCollection = db.collection('equipment');
    const upgradesCollection = db.collection('upgrades');
    const snapshotsCollection = db.collection('daily_snapshots');

    // Fetch current active data and archived data for the day
    const [todos, archivedTasks, equipment, upgrades] = await Promise.all([
        todosCollection.find({}).toArray(),
        archivedTasksCollection.find({ archiveDate: dateString }).toArray(),
        equipmentCollection.find({}).toArray(),
        upgradesCollection.find({}).toArray()
    ]);

    // Combine active and archived tasks for complete daily picture
    const allDayTasks = [...todos, ...archivedTasks];

    // Calculate task metrics using all tasks (active + archived)
    const taskMetrics = calculateTaskMetrics(allDayTasks, snapshotDate, dateString);

    // Calculate equipment metrics
    const equipmentMetrics = calculateEquipmentMetrics(equipment);

    // Calculate upgrade metrics
    const upgradeMetrics = calculateUpgradeMetrics(upgrades);

    // Calculate productivity metrics using all tasks
    const productivityMetrics = calculateProductivityMetrics(allDayTasks, snapshotDate);

    // Calculate archival metrics
    const archivalMetrics = calculateArchivalMetrics(archivedTasks, todos);
    
    // Create comprehensive snapshot
    const snapshot = {
        date: dateString,
        timestamp: snapshotDate.toISOString(),
        
        // Raw data snapshots (includes both active and archived tasks for the day)
        tasks: {
            total: allDayTasks.length,
            completed: allDayTasks.filter(t => t.completed).length,
            pending: allDayTasks.filter(t => !t.completed).length,
            active: todos.length,
            archived: archivedTasks.length,
            data: allDayTasks.map(task => ({
                id: task._id,
                originalId: task.originalId, // For archived tasks
                task: task.task,
                problem: task.problem,
                impact: task.impact,
                completed: task.completed,
                createdAt: task.createdAt,
                completedAt: task.completedAt,
                archivedAt: task.archivedAt, // For archived tasks
                isArchived: !!task.archivedAt,
                category: categorizeTask(task)
            }))
        },
        
        equipment: {
            total: equipment.length,
            byStatus: equipmentMetrics.statusDistribution,
            byType: equipmentMetrics.typeDistribution,
            data: equipment.map(item => ({
                id: item._id,
                name: item.name,
                type: item.type,
                status: item.status,
                location: item.location,
                lastUpdated: item.lastUpdated
            }))
        },
        
        upgrades: {
            total: upgrades.length,
            byStatus: upgradeMetrics.statusDistribution,
            pending: upgrades.filter(u => u.status === 'pending').length,
            approved: upgrades.filter(u => u.status === 'approved').length,
            data: upgrades.map(upgrade => ({
                id: upgrade._id,
                equipment: upgrade.equipment,
                requestedBy: upgrade.requestedBy,
                status: upgrade.status,
                priority: upgrade.priority,
                requestDate: upgrade.requestDate
            }))
        },
        
        // Calculated metrics
        metrics: {
            task: taskMetrics,
            equipment: equipmentMetrics,
            upgrade: upgradeMetrics,
            productivity: productivityMetrics,
            archival: archivalMetrics
        },
        
        // Daily insights
        insights: generateDailyInsights(taskMetrics, equipmentMetrics, upgradeMetrics),
        
        // Metadata
        createdAt: snapshotDate.toISOString(),
        version: '1.0'
    };

    // Store snapshot
    await snapshotsCollection.replaceOne(
        { date: dateString },
        snapshot,
        { upsert: true }
    );

    return snapshot;
}

function calculateTaskMetrics(todos, snapshotDate, dateString) {
    const today = snapshotDate.toISOString().split('T')[0];
    const yesterday = new Date(snapshotDate);
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayString = yesterday.toISOString().split('T')[0];

    // Tasks created today
    const tasksCreatedToday = todos.filter(task => 
        task.createdAt && task.createdAt.startsWith(today)
    ).length;

    // Tasks completed today
    const tasksCompletedToday = todos.filter(task => 
        task.completed && task.completedAt && task.completedAt.startsWith(today)
    ).length;

    // Task categories
    const categories = {
        'Technical Issues': 0,
        'Maintenance': 0,
        'User Support': 0,
        'System Updates': 0,
        'Other': 0
    };

    todos.forEach(task => {
        const category = categorizeTask(task);
        categories[category]++;
    });

    // Average completion time (mock calculation)
    const avgCompletionTime = calculateAverageCompletionTime(todos);

    return {
        total: todos.length,
        completed: todos.filter(t => t.completed).length,
        pending: todos.filter(t => !t.completed).length,
        completionRate: todos.length > 0 ? (todos.filter(t => t.completed).length / todos.length) * 100 : 0,
        createdToday: tasksCreatedToday,
        completedToday: tasksCompletedToday,
        categories: categories,
        avgCompletionTime: avgCompletionTime,
        productivity: tasksCompletedToday > 0 ? Math.min(tasksCompletedToday * 20, 100) : 0
    };
}

function calculateEquipmentMetrics(equipment) {
    const statusDistribution = {};
    const typeDistribution = {};

    equipment.forEach(item => {
        // Status distribution
        const status = item.status || 'Active';
        statusDistribution[status] = (statusDistribution[status] || 0) + 1;

        // Type distribution
        const type = item.type || 'Unknown';
        typeDistribution[type] = (typeDistribution[type] || 0) + 1;
    });

    return {
        total: equipment.length,
        statusDistribution,
        typeDistribution,
        maintenanceNeeded: equipment.filter(item => item.status === 'Maintenance').length,
        activeEquipment: equipment.filter(item => item.status === 'Active' || !item.status).length
    };
}

function calculateUpgradeMetrics(upgrades) {
    const statusDistribution = {};
    const priorityDistribution = {};

    upgrades.forEach(upgrade => {
        // Status distribution
        const status = upgrade.status || 'Pending';
        statusDistribution[status] = (statusDistribution[status] || 0) + 1;

        // Priority distribution
        const priority = upgrade.priority || 'Medium';
        priorityDistribution[priority] = (priorityDistribution[priority] || 0) + 1;
    });

    return {
        total: upgrades.length,
        statusDistribution,
        priorityDistribution,
        pending: upgrades.filter(u => u.status === 'pending' || !u.status).length,
        approved: upgrades.filter(u => u.status === 'approved').length,
        highPriority: upgrades.filter(u => u.priority === 'High').length
    };
}

function calculateProductivityMetrics(todos, snapshotDate) {
    const completedTasks = todos.filter(t => t.completed);
    const totalTasks = todos.length;
    
    // Calculate productivity score
    const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
    
    // Tasks with impact analysis
    const tasksWithImpact = todos.filter(t => t.impact && t.impact.trim().length > 0).length;
    const impactRate = totalTasks > 0 ? (tasksWithImpact / totalTasks) * 100 : 0;
    
    // Quality score based on completeness
    const qualityScore = calculateQualityScore(todos);

    return {
        completionRate: Math.round(completionRate),
        impactRate: Math.round(impactRate),
        qualityScore: Math.round(qualityScore),
        efficiency: Math.round((completionRate + impactRate + qualityScore) / 3),
        totalTasksProcessed: completedTasks.length,
        averageTasksPerDay: calculateAverageTasksPerDay(todos, snapshotDate)
    };
}

function categorizeTask(task) {
    const content = ((task.task || '') + ' ' + (task.problem || '') + ' ' + (task.impact || '')).toLowerCase();
    
    if (content.includes('technical') || content.includes('bug') || content.includes('error')) {
        return 'Technical Issues';
    } else if (content.includes('maintenance') || content.includes('update') || content.includes('patch')) {
        return 'Maintenance';
    } else if (content.includes('user') || content.includes('support') || content.includes('help')) {
        return 'User Support';
    } else if (content.includes('system') || content.includes('server') || content.includes('network')) {
        return 'System Updates';
    } else {
        return 'Other';
    }
}

function calculateAverageCompletionTime(todos) {
    // Mock calculation - in real implementation, track actual completion times
    const completedTasks = todos.filter(t => t.completed && t.createdAt && t.completedAt);
    if (completedTasks.length === 0) return 0;
    
    // Simulate completion time calculation
    return Math.round(Math.random() * 4 + 1); // 1-5 hours average
}

function calculateQualityScore(todos) {
    if (todos.length === 0) return 0;
    
    let qualityPoints = 0;
    todos.forEach(task => {
        // Points for having task description
        if (task.task && task.task.trim().length > 0) qualityPoints += 25;
        
        // Points for having problem description
        if (task.problem && task.problem.trim().length > 0) qualityPoints += 25;
        
        // Points for having impact/solution
        if (task.impact && task.impact.trim().length > 0) qualityPoints += 25;
        
        // Points for completion
        if (task.completed) qualityPoints += 25;
    });
    
    return (qualityPoints / (todos.length * 100)) * 100;
}

function calculateAverageTasksPerDay(todos, currentDate) {
    // Calculate based on date range of tasks
    const dates = todos
        .filter(t => t.createdAt)
        .map(t => new Date(t.createdAt))
        .sort((a, b) => a - b);
    
    if (dates.length === 0) return 0;
    
    const firstDate = dates[0];
    const daysDiff = Math.max(1, Math.ceil((currentDate - firstDate) / (1000 * 60 * 60 * 24)));
    
    return Math.round(todos.length / daysDiff * 10) / 10; // Round to 1 decimal
}

function calculateArchivalMetrics(archivedTasks, activeTasks) {
    const totalArchived = archivedTasks.length;
    const totalActive = activeTasks.length;
    const totalTasks = totalArchived + totalActive;

    // Calculate archival efficiency
    const completedArchived = archivedTasks.filter(t => t.completed).length;
    const incompleteArchived = archivedTasks.filter(t => !t.completed).length;

    // Calculate archival rate
    const archivalRate = totalTasks > 0 ? (totalArchived / totalTasks) * 100 : 0;

    // Calculate quality metrics
    const archivedWithProblem = archivedTasks.filter(t => t.problem && t.problem.trim()).length;
    const archivedWithSolution = archivedTasks.filter(t => t.impact && t.impact.trim()).length;

    return {
        totalArchived,
        totalActive,
        archivalRate: Math.round(archivalRate),
        completedArchived,
        incompleteArchived,
        archivedWithProblem,
        archivedWithSolution,
        archivalEfficiency: totalArchived > 0 ? Math.round((completedArchived / totalArchived) * 100) : 0,
        dataQuality: totalArchived > 0 ? Math.round(((archivedWithProblem + archivedWithSolution) / (totalArchived * 2)) * 100) : 0
    };
}

function generateDailyInsights(taskMetrics, equipmentMetrics, upgradeMetrics) {
    const insights = [];
    
    // Task insights
    if (taskMetrics.completionRate > 80) {
        insights.push({
            type: 'positive',
            category: 'tasks',
            message: `Excellent task completion rate of ${taskMetrics.completionRate.toFixed(1)}%`,
            recommendation: 'Maintain current productivity levels'
        });
    } else if (taskMetrics.completionRate < 50) {
        insights.push({
            type: 'warning',
            category: 'tasks',
            message: `Low task completion rate of ${taskMetrics.completionRate.toFixed(1)}%`,
            recommendation: 'Consider breaking down large tasks or reviewing workload'
        });
    }
    
    // Equipment insights
    if (equipmentMetrics.maintenanceNeeded > 0) {
        insights.push({
            type: 'warning',
            category: 'equipment',
            message: `${equipmentMetrics.maintenanceNeeded} equipment items need maintenance`,
            recommendation: 'Schedule maintenance to prevent downtime'
        });
    }
    
    // Upgrade insights
    if (upgradeMetrics.highPriority > 0) {
        insights.push({
            type: 'info',
            category: 'upgrades',
            message: `${upgradeMetrics.highPriority} high-priority upgrade requests pending`,
            recommendation: 'Review and prioritize urgent upgrade requests'
        });
    }
    
    return insights;
}
